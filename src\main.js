import Vue from "vue";

// 性能监控 - 开发环境
if (process.env.NODE_ENV === "development") {
  const { PerformancePlugin } = require("@/utils/performance");
  Vue.use(PerformancePlugin);
  Vue.prototype.$performance.mark("app-init-start");
}

import "normalize.css/normalize.css"; // A modern alternative to CSS resets

// Element UI 全量引入（优化版本）
import ElementUI from "@/plugins/element";
Vue.use(ElementUI);

// 中文化消息插件
import MessagePlugin from "@/utils/messagePlugin";
Vue.use(MessagePlugin);

// 导入Context7面包屑插件
import { BreadcrumbContext7Plugin } from "@/utils/breadcrumb-context7";
Vue.use(BreadcrumbContext7Plugin, {
  enableCache: true,
  maxLevel: 5,
  showHome: true,
  homeTitle: "首页",
  iconMapping: {
    home: "home",
    dashboard: "dashboard",
    dataShow: "el-icon-data-line",
    userList: "el-icon-user",
    ProxyList: "el-icon-s-custom",
    chat: "el-icon-phone-outline",
    uploadVersion: "el-icon-upload2",
  },
});

// 延迟加载非关键资源
const loadNonCriticalResources = () => {
  // 异步加载 i18n
  import("@/utils/i18").catch((err) => console.warn("i18n load failed:", err));
};

import "@/styles/index.scss"; // global css

import App from "./App";
import store from "./store";
import router from "./router";

import "@/icons"; // icon
import "@/permission"; // permission control

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
// Mock相关代码已删除，现在使用真实后端API

// 异步加载 JsonExcel 组件
const loadJsonExcel = async () => {
  try {
    const JsonExcel = await import("vue-json-excel");
    Vue.component("downloadExcel", JsonExcel.default);
  } catch (err) {
    console.warn("JsonExcel load failed:", err);
  }
};

Vue.config.productionTip = false;

const app = new Vue({
  el: "#app",
  router,
  store,
  async mounted() {
    // 性能监控
    if (process.env.NODE_ENV === "development") {
      this.$performance.mark("app-mounted");
      this.$performance.measure("app-init", "app-init-start", "app-mounted");
    }

    // 应用挂载后加载非关键资源
    loadNonCriticalResources();
    await loadJsonExcel();

    // 生成性能报告
    if (process.env.NODE_ENV === "development") {
      setTimeout(() => {
        this.$performance.generateReport();
      }, 1000);
    }
  },
  render: (h) => h(App),
});
