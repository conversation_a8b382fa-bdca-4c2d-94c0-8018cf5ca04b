﻿<template>
  <div class="app-container">
    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <div class="search-card">
        <div class="search-header">
          <i class="el-icon-search search-icon" />
          <span class="search-title">订单查询</span>
        </div>

        <div class="search-form">
          <div class="search-row">
            <div class="search-item">
              <label class="search-label">手机号</label>
              <el-input
                v-model="phone"
                placeholder="请输入手机号"
                class="search-input"
                clearable
              >
                <i
                  slot="prefix"
                  class="el-input__icon el-icon-mobile-phone"
                />
              </el-input>
            </div>

            <div class="search-item">
              <label class="search-label">用户名</label>
              <el-input
                v-model="userName"
                placeholder="请输入用户名"
                class="search-input"
                clearable
              >
                <i slot="prefix" class="el-input__icon el-icon-user" />
              </el-input>
            </div>

            <div class="search-item">
              <label class="search-label">订单编号</label>
              <el-input
                v-model="outTradeNum"
                placeholder="请输入订单编号"
                class="search-input"
                clearable
              >
                <i slot="prefix" class="el-input__icon el-icon-document" />
              </el-input>
            </div>

            <div class="search-item">
              <label class="search-label">日期范围</label>
              <el-date-picker
                v-model="value1"
                :default-value="new Date()"
                :unlink-panels="true"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="search-date-picker"
                @change="change"
              />
            </div>
          </div>

          <div class="search-buttons">
            <el-button
              type="primary"
              class="search-btn"
              icon="el-icon-search"
              @click="searchOrder"
            >
              查询订单
            </el-button>
            <el-button
              type="success"
              class="export-btn"
              icon="el-icon-download"
              @click="downLoadExcex()"
            >
              下载Excel表
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-card">
        <div class="table-header">
          <div class="table-title">
            <i class="el-icon-s-order" />
            <span>自营订单列表</span>
          </div>
        </div>

        <el-table
          v-loading="loading"
          :data="showData"
          stripe
          style="width: 100%"
          class="modern-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#374151',
            fontWeight: '600',
          }"
          :row-style="{ height: '60px' }"
        >
          <el-table-column
            prop="outTradeNum"
            width="130"
            label="本地订单编号"
            align="center"
          >
            <template slot-scope="scope">
              <span class="order-no">{{ scope.row.outTradeNum }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="systemOrderNo"
            label="系统单号"
            align="center"
            min-width="150"
          >
            <template slot-scope="scope">
              <span class="system-order">{{ scope.row.systemOrderNo }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="goodsId"
            label="商品ID"
            align="center"
            width="100"
          >
            <template slot-scope="scope">
              <span class="goods-id">{{ scope.row.goodsId }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="userId"
            label="用户ID"
            align="center"
            width="100"
          >
            <template slot-scope="scope">
              <span class="user-id">{{ scope.row.userId }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="userPhone"
            label="手机号"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <div class="phone-info">
                <i class="el-icon-mobile-phone phone-icon" />
                <span>{{ scope.row.userPhone }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="userName"
            label="姓名"
            align="center"
            width="100"
          >
            <template slot-scope="scope">
              <span class="user-name">{{ scope.row.userName }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="mailAddr"
            label="邮寄地址"
            align="center"
            min-width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span class="address-text">{{ scope.row.mailAddr }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="mailNo"
            label="快递编号"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span class="mail-no">{{ scope.row.mailNo || "未绑定" }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="goodsName"
            label="商品名"
            align="center"
            min-width="150"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <div class="goods-info">
                <i class="el-icon-goods goods-icon" />
                <span class="goods-name">{{ scope.row.goodsName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="goodsNum"
            label="数量"
            align="center"
            width="80"
          >
            <template slot-scope="scope">
              <span class="quantity">{{ scope.row.goodsNum }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="facePrice"
            label="面值价格"
            align="center"
            width="100"
          >
            <template slot-scope="scope">
              <span
                class="price-text face-price"
              >¥{{ scope.row.facePrice }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="freight"
            label="运费"
            align="center"
            width="80"
          >
            <template slot-scope="scope">
              <span class="freight-text">¥{{ scope.row.freight }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="realPay"
            label="实付价格"
            align="center"
            width="100"
          >
            <template slot-scope="scope">
              <span class="price-text real-pay">¥{{ scope.row.realPay }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="payTime"
            label="支付时间"
            align="center"
            width="160"
          >
            <template slot-scope="scope">
              <span class="time-text">{{ scope.row.payTime }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="orderStatus"
            label="订单状态"
            align="center"
            width="100"
          >
            <template v-slot="scope">
              <el-tag
                :type="getOrderStatusType(scope.row.orderStatus)"
                size="small"
                class="status-tag"
              >
                {{ getOrderStatus(scope.row.orderStatus) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="usedIntegral"
            label="消耗积分"
            align="center"
            width="100"
          >
            <template slot-scope="scope">
              <span class="integral-text">{{ scope.row.usedIntegral }}</span>
            </template>
          </el-table-column>

          <el-table-column label="退款金额" align="center" width="100">
            <template v-slot="scope">
              <span class="refund-amount">{{
                getRefund(scope.row.refund)
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="refundTime"
            label="退款时间"
            align="center"
            width="160"
          >
            <template slot-scope="scope">
              <span class="time-text">{{ scope.row.refundTime || "-" }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="refundStatus"
            label="退款状态"
            align="center"
            width="100"
          >
            <template v-slot="scope">
              <el-tag
                :type="
                  getRefundStatus(scope.row.refundStatus) === '无退款'
                    ? 'success'
                    : 'danger'
                "
                size="small"
                class="status-tag"
              >
                {{
                  getRefundStatus(scope.row.refundStatus) === "无退款"
                    ? "正常订单"
                    : getRefundStatus(scope.row.refundStatus)
                }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="refundId"
            width="120"
            label="退款订单号"
            align="center"
          >
            <template slot-scope="scope">
              <span class="refund-id">{{ scope.row.refundId || "-" }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="payPlatform"
            label="支付平台"
            align="center"
            width="100"
          >
            <template slot-scope="scope">
              <span class="pay-platform">{{ scope.row.payPlatform }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            width="220"
            fixed="right"
            align="center"
          >
            <template v-slot="scope">
              <el-button
                type="primary"
                size="mini"
                :disabled="
                  scope.row.orderStatus === 'WAIT_PAY' ||
                    scope.row.orderStatus === 'PAY_FAIL'
                "
                class="operation-btn bind-btn"
                @click="bindMailNo(scope.row)"
              >
                绑定快递
              </el-button>
              <el-button
                type="danger"
                plain
                size="mini"
                :disabled="
                  scope.row.orderStatus === 'WAIT_PAY' ||
                    scope.row.orderStatus === 'PAY_FAIL' ||
                    scope.row.orderStatus === 'SUCCESS'
                "
                class="operation-btn refund-btn"
                @click="clickRefund(scope.row)"
              >
                退款
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <el-pagination
        background
        :current-page="currentPage"
        :page-size="pagesize"
        :page-sizes="[5, 10, 20, 30, 40]"
        :total="totalnum"
        layout="total, prev, pager, next, jumper, sizes"
        class="modern-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import * as xlsx from 'xlsx'
import { saveAs } from 'file-saver'
import {
  getOrderList,
  getSelfOrderExcel,
  getSelfOrderCondition,
  refund,
  updateSelfOrder
} from '@/api/selfOperated'

export default {
  data() {
    return {
      showData: [],
      phone: '',
      userName: '',
      outTradeNum: '',
      loading: false,
      currentPage: 1,
      pagesize: 10,
      totalnum: 0,
      value1: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      options: [
        {
          label: '支付成功',
          value: 'PAY'
        },
        {
          label: '支付失败',
          value: 'PAY_FAIL'
        },
        {
          label: '待支付',
          value: 'WAIT_PAY'
        },
        {
          label: '充值中',
          value: 'WAIT_TOPUP'
        },
        {
          label: '充值失败',
          value: 'FAIL_ORDER'
        },
        {
          label: '充值成功',
          value: 'SUCCESS'
        },
        {
          label: '全部',
          value: 'all'
        }
      ],
      refundStatus: 'all'
    }
  },
  mounted() {
    this.fetchOrderList()
  },
  methods: {
    change(o) {
      console.log(o)
      console.log(this.value1)
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始，需加1，并确保是两位数
      const day = String(date.getDate()).padStart(2, '0') // 确保日期是两位数
      const hours = String(date.getHours()).padStart(2, '0') // 确保小时是两位数
      const minutes = String(date.getMinutes()).padStart(2, '0') // 确保分钟是两位数
      const seconds = String(date.getSeconds()).padStart(2, '0') // 确保秒数是两位数

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    searchOrder() {
      this.currentPage = 1
      this.loading = true

      // 构建请求参数
      const params = {
        page: this.currentPage,
        limit: this.pagesize
      }

      // 如果有手机号，添加到请求参数
      if (this.phone) {
        params.userPhone = this.phone
      }

      // 如果有用户名，添加到请求参数
      if (this.userName) {
        params.userName = this.userName
      }

      // 如果有订单编号，添加到请求参数
      if (this.outTradeNum) {
        params.outTradeNum = this.outTradeNum
      }

      // 如果有日期范围，添加到请求参数
      if (this.value1 && this.value1.length === 2) {
        params.startTime = this.formatDate(this.value1[0])
        params.endTime = this.formatDate(this.value1[1])
      }

      // 调用条件查询API
      getSelfOrderCondition(params)
        .then((res) => {
          this.loading = false
          if (res && res.data) {
            // 适配返回的数据格式
            this.showData = res.data.list || []
            this.totalnum = res.data.total || 0
          } else {
            this.$message.error('查询订单失败')
            this.showData = []
            this.totalnum = 0
          }
        })
        .catch((err) => {
          this.loading = false
          console.error('查询订单出错:', err)
          this.$message.error('查询订单失败')
          this.showData = []
          this.totalnum = 0
        })
    },
    getRefund(obj) {
      if (obj == '' || obj == null || obj == undefined) {
        return 0
      } else {
        return obj
      }
    },
    // 获取订单列表
    fetchOrderList(page, pageSize) {
      this.loading = true
      // 构建请求参数
      const params = {
        page: page || this.currentPage,
        limit: pageSize || this.pagesize
      }

      // 如果有手机号，添加到请求参数
      if (this.phone) {
        params.userPhone = this.phone
      }

      // 如果有用户名，添加到请求参数
      if (this.userName) {
        params.userName = this.userName
      }

      // 如果有订单编号，添加到请求参数
      if (this.outTradeNum) {
        params.outTradeNum = this.outTradeNum
      }

      // 如果有退款状态，添加到请求参数
      if (this.refundStatus && this.refundStatus !== 'all') {
        params.refundStatus = this.refundStatus
      }

      // 如果有日期范围，添加到请求参数
      if (this.value1 && this.value1.length === 2) {
        params.startTime = this.formatDate(this.value1[0])
        params.endTime = this.formatDate(this.value1[1])
      }

      // 调用条件查询API
      getSelfOrderCondition(params)
        .then((res) => {
          this.loading = false
          if (res && res.data) {
            // 适配返回的数据格式
            this.showData = res.data.list || []
            this.totalnum = res.data.total || 0
          } else {
            this.$message.error('查询订单失败')
            this.showData = []
            this.totalnum = 0
          }
        })
        .catch((err) => {
          this.loading = false
          console.error('查询订单出错:', err)
          this.$message.error('查询订单失败')
          this.showData = []
          this.totalnum = 0
        })
    },
    handleSizeChange: function(pagesize) {
      this.pagesize = pagesize
      this.fetchOrderList(this.currentPage, this.pagesize)
    },
    // 监听页数变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchOrderList(this.currentPage, this.pagesize)
    },
    // 订单状态映射
    getOrderStatus(obj) {
      if (obj === 'PAY') {
        return '支付成功'
      }
      if (obj === 'PAY_FAIL') {
        return '支付失败'
      }
      if (obj === 'WAIT_PAY') {
        return '待支付'
      }
      if (obj === 'WAIT_TOPUP') {
        return '充值中'
      }
      if (obj === 'FAIL_ORDER') {
        return '充值失败'
      }
      if (obj === 'SUCCESS') {
        return '充值成功'
      }
    },
    // 订单状态类型映射（用于标签颜色）
    getOrderStatusType(status) {
      const statusMap = {
        PAY: 'success',
        SUCCESS: 'success',
        WAIT_PAY: 'warning',
        WAIT_TOPUP: 'info',
        PAY_FAIL: 'danger',
        FAIL_ORDER: 'danger'
      }
      return statusMap[status] || 'info'
    },
    // 退货状态映射
    getRefundStatus(obj) {
      if (obj === 'WAIT_REFUND') {
        return '待退款'
      }
      if (obj === 'REFUND') {
        return '已退款'
      }
      if (obj === 'FAILED') {
        return '退款失败'
      }
      return '无退款'
    },
    // 下载订单excel表
    downLoadExcex() {
      const h = this.$createElement
      this.$msgbox({
        title: '提示',
        message: h('p', null, [
          h(
            'span',
            {
              style: 'color: black'
            },
            '是否确认导出excel数据'
          )
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '导出中...'

            const data = {}

            // 添加手机号参数
            if (this.phone) {
              data.userPhone = this.phone
            }

            // 添加用户名参数
            if (this.userName) {
              data.userName = this.userName
            }

            // 添加订单编号参数
            if (this.outTradeNum) {
              data.outTradeNum = this.outTradeNum
            }

            // 添加日期范围参数
            if (this.value1 && this.value1.length === 2) {
              data.startTime = this.formatDate(this.value1[0])
              data.endTime = this.formatDate(this.value1[1])
            }

            console.log('下载Excel请求参数:', data)

            // 使用getSelfOrderExcel方法
            getSelfOrderExcel(data)
              .then((response) => {
                const data = response.data.data
                // 使用xlsx库的工作簿和工作表API
                const ws = xlsx.utils.aoa_to_sheet(data) // 将数组转换为工作表
                const wb = xlsx.utils.book_new() // 创建一个新的工作簿
                xlsx.utils.book_append_sheet(wb, ws, 'Sheet1') // 将工作表添加到工作簿

                // 生成Excel文件
                const wbout = xlsx.write(wb, {
                  bookType: 'xlsx',
                  type: 'binary'
                })

                // 使用file-saver保存文件
                function s2ab(s) {
                  const buf = new ArrayBuffer(s.length)
                  const view = new Uint8Array(buf)
                  for (let i = 0; i < s.length; i++) { view[i] = s.charCodeAt(i) & 0xff }
                  return buf
                }

                const dataName = '自营订单导出数据.xlsx'
                saveAs(
                  new Blob([s2ab(wbout)], {
                    type: 'application/octet-stream'
                  }),
                  dataName
                )
                done()
                instance.confirmButtonLoading = false
              })
              .catch((err) => {
                console.error('导出Excel出错:', err)
                this.$message.error('导出数据失败')
                done()
                instance.confirmButtonLoading = false
              })
          } else {
            done()
          }
        }
      })
        .then((action) => {
          // 成功提示已在请求成功后显示
        })
        .catch((action) => {
          this.$message.warning('取消导出数据')
        })
    },
    // 退款操作
    clickRefund(row) {
      console.log(row)
      if (row.refund >= row.realPay) {
        this.$message.error('该订单以达到最大退款金额')
        return
      }
      this.$prompt('是否对该订单申请退款?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入退款金额',
        inputPattern: /^(0|([1-9][0-9]*))(\.[\d]+)?$/
      })
        .then(({ value }) => {
          console.log('打印信息')
          console.log(row)
          console.log(value)
          this.torefund(row, value)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          })
        })
    },
    // 退款
    torefund(order, value) {
      if (value > order.realPay - order.refund) {
        this.$message.error('无法大于订单总金额')
        return
      }
      const data = {
        mchnt_order_no: order.outTradeNum,
        refund_amt: value,
        total_amt: order.realPay + '',
        order_time: order.payTime
      }
      this.loading = true
      // 调用退款API
      refund(data)
        .then((res) => {
          this.loading = false
          if (res.data && res.data.code === 200) {
            this.$message.success('退款申请成功')
          } else {
            this.$message.error(res.message || '退款申请失败')
          }
          // 刷新订单列表
          this.fetchOrderList(this.currentPage, this.pagesize)
        })
        .catch((err) => {
          this.loading = false
          console.error('退款出错:', err)
          this.$message.error('退款申请失败')
          // 刷新订单列表
          this.fetchOrderList(this.currentPage, this.pagesize)
        })
    },
    // 绑定快递编号
    bindMailNo(row) {
      this.$prompt('请输入快递编号', '绑定快递', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S+/,
        inputErrorMessage: '快递编号不能为空'
      })
        .then(({ value }) => {
          const params = {
            orderId: row.outTradeNum,
            mailNo: value
          }

          updateSelfOrder(params)
            .then((res) => {
              if (res && res.data && res.data.code === 200) {
                this.$message.success('绑定快递成功')
                this.fetchOrderList(this.currentPage, this.pagesize)
              } else {
                this.$message.error(res.data.message || '绑定快递失败')
              }
            })
            .catch((err) => {
              console.error('绑定快递出错:', err)
              this.$message.error('绑定快递失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消绑定')
        })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 6px;
  background-color: #f5f7fa;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 8px;
}

.search-card {
  background: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

.search-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.search-icon {
  font-size: 20px;
  color: #409eff;
  margin-right: 6px;
}

.search-title {
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.search-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
}

.search-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-label {
  font-size: 0.8rem;
  font-weight: 500;
  color: #606266;
}

.search-input {
  border-radius: 6px;
}

.search-date-picker {
  width: 100%;
  border-radius: 6px;
}

.search-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.search-btn,
.export-btn {
  border-radius: 8px;
  padding: 12px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.search-btn {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
}

.search-btn:hover {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);

}

.export-btn {
  background: linear-gradient(135deg, #909399 0%, #909399 100%);
  border: none;
  color: #2c3e50;
}

.export-btn:hover {
  background: linear-gradient(135deg, #909399 0%, #909399 100%);

}

/* 表格区域样式 */
.table-section {
  margin-bottom: 8px;
}

.table-card {
  background: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.table-title {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  color: #303133;
}

.table-title i {
  font-size: 20px;
  color: #409eff;
  margin-right: 6px;
}

.modern-table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ebeef5;
}

.modern-table .el-table__header-wrapper {
  border-radius: 8px 8px 0 0;
}

.modern-table .el-table__row {
  transition: all 0.3s ease;
}

.modern-table .el-table__row:hover {
  background-color: #f8faff !important;

  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.order-no {
  font-family: "Monaco", "Consolas", monospace;
  font-size: 12px;
  color: #606266;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.system-order {
  font-family: "Monaco", "Consolas", monospace;
  font-size: 11px;
  color: #909399;
}

.goods-id,
.user-id {
  font-family: "Monaco", "Consolas", monospace;
  font-size: 12px;
  color: #606266;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.phone-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.phone-icon {
  color: #409eff;
  font-size: 0.8rem;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.address-text {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.mail-no {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.goods-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.goods-icon {
  color: #67c23a;
  font-size: 0.8rem;
}

.goods-name {
  color: #303133;
  font-size: 12px;
}

.quantity {
  font-weight: 600;
  color: #303133;
  background: #ecf5ff;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
}

.price-text {
  font-weight: 600;
  font-size: 12px;
}

.face-price {
  color: #f56c6c;
}

.real-pay {
  color: #67c23a;
}

.freight-text {
  color: #909399;
  font-size: 11px;
}

.time-text {
  color: #909399;
  font-size: 11px;
}

.status-tag {
  border-radius: 8px;
  padding: 4px 12px;
  font-size: 11px;
  font-weight: 500;
}

.integral-text {
  color: #e6a23c;
  font-weight: 500;
  font-size: 12px;
}

.refund-amount {
  color: #f56c6c;
  font-weight: 500;
  font-size: 12px;
}

.refund-id {
  font-family: "Monaco", "Consolas", monospace;
  font-size: 11px;
  color: #909399;
}

.pay-platform {
  color: #409eff;
  font-size: 12px;
  font-weight: 500;
}

.operation-btn {
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 11px;
  font-weight: 500;
  margin-right: 6px;
}

.bind-btn {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
}

.bind-btn:hover {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
}

.refund-btn {
  background: linear-gradient(135deg, #f56c6c 0%, #f56c6c 100%);
  border: none;
  color: white;
}

.refund-btn:hover {
  background: linear-gradient(135deg, #f56c6c 0%, #f56c6c 100%);
}

/* 分页区域样式 */
.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.modern-pagination {
  background: white;
  border-radius: 8px;
  padding: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-row {
    grid-template-columns: 1fr;
  }

  .search-buttons {
    justify-content: stretch;
  }

  .search-btn,
  .export-btn {
    flex: 1;
  }
}
</style>

