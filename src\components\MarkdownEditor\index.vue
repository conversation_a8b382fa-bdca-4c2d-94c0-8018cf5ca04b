<template>
  <div
    :id="id"
    class="w-full h-full min-h-[300px] border border-gray-200 rounded-lg shadow-sm bg-white overflow-hidden transition-all duration-200 ease-in-out"
    :class="{
      'opacity-60 pointer-events-none': disabled,
      'border-red-300 shadow-red-100': hasError,
      'border-blue-300 shadow-blue-100': isFocused,
    }"
  />
</template>

<script>
import "codemirror/lib/codemirror.css";
import "@toast-ui/editor/dist/toastui-editor.css";
import "tui-color-picker/dist/tui-color-picker.css";
import "@toast-ui/editor-plugin-color-syntax/dist/toastui-editor-plugin-color-syntax.css";

import colorSyntax from "@toast-ui/editor-plugin-color-syntax";
import Editor from "@toast-ui/editor";
import defaultOptions from "./default-options";

const EDIT_MODES = ["markdown", "wysiwyg"];
const SUPPORTED_LANGUAGES = ["zh-C<PERSON>", "en-US", "ja-<PERSON>", "ko-K<PERSON>"];

export default {
  name: "MarkdownEditor",
  props: {
    value: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      required: false,
      default() {
        return `markdown-editor-${Date.now()}-${Math.random()
          .toString(36)
          .substr(2, 9)}`;
      },
    },
    options: {
      type: Object,
      default: () => ({}),
      validator(value) {
        return typeof value === "object" && value !== null;
      },
    },
    mode: {
      type: String,
      default: "markdown",
      validator(value) {
        return EDIT_MODES.includes(value);
      },
    },
    height: {
      type: [String, Number],
      default: "300px",
      validator(value) {
        if (typeof value === "number") {
          return value > 0;
        }
        return (
          typeof value === "string" &&
          /^\d+(\.\d+)?(px|em|rem|%|vh)$/.test(value)
        );
      },
    },
    language: {
      type: String,
      default: "zh-CN",
      validator(value) {
        return SUPPORTED_LANGUAGES.includes(value);
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: "请输入内容...",
    },
  },
  data() {
    return {
      editor: null,
      isLoading: false,
      hasError: false,
      isFocused: false,
    };
  },
  computed: {
    editorOptions() {
      const options = { ...defaultOptions, ...this.options };
      options.initialEditType = this.mode;
      options.height =
        typeof this.height === "number" ? `${this.height}px` : this.height;
      options.language = this.language;
      options.placeholder = this.placeholder;
      return options;
    },
  },
  watch: {
    value: {
      handler(newValue, preValue) {
        if (
          newValue !== preValue &&
          this.editor &&
          newValue !== this.editor.getMarkdown()
        ) {
          this.setMarkdown(newValue);
        }
      },
      immediate: false,
    },
    language(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.reinitializeEditor();
      }
    },
    height(newValue) {
      if (this.editor) {
        const heightValue =
          typeof newValue === "number" ? `${newValue}px` : newValue;
        this.editor.height(heightValue);
      }
    },
    mode(newValue) {
      if (this.editor) {
        this.editor.changeMode(newValue);
      }
    },
    disabled(newValue) {
      this.updateDisabledState(newValue);
    },
  },
  mounted() {
    this.initEditor();
  },
  beforeDestroy() {
    this.destroyEditor();
  },
  methods: {
    async initEditor() {
      try {
        this.isLoading = true;
        this.hasError = false;

        const element = document.getElementById(this.id);
        if (!element) {
          throw new Error(`无法找到 ID 为 ${this.id} 的元素`);
        }

        this.editor = new Editor({
          el: element,
          ...this.editorOptions,
          plugins: [colorSyntax],
        });

        if (this.value) {
          this.editor.setMarkdown(this.value);
        }

        this.bindEditorEvents();
        this.updateDisabledState(this.disabled);

        this.$emit("ready", this.editor);
      } catch (error) {
        console.error("初始化编辑器失败:", error);
        this.hasError = true;
        this.$emit("error", { type: "init", message: error.message, error });
      } finally {
        this.isLoading = false;
      }
    },

    bindEditorEvents() {
      if (!this.editor) return;

      this.editor.on("change", () => {
        const content = this.editor.getMarkdown();
        this.$emit("input", content);
        this.$emit("change", content);
      });

      this.editor.on("focus", () => {
        this.isFocused = true;
        this.$emit("focus");
      });

      this.editor.on("blur", () => {
        this.isFocused = false;
        this.$emit("blur");
      });

      this.editor.on("load", () => {
        this.$emit("load");
      });
    },

    updateDisabledState(disabled) {
      if (this.editor) {
        const element = document.getElementById(this.id);
        if (element) {
          element.style.pointerEvents = disabled ? "none" : "auto";
        }
      }
    },

    reinitializeEditor() {
      const currentContent = this.getMarkdown();
      this.destroyEditor();
      this.$nextTick(() => {
        this.initEditor().then(() => {
          if (currentContent) {
            this.setMarkdown(currentContent);
          }
        });
      });
    },

    destroyEditor() {
      if (!this.editor) return;

      try {
        this.editor.off("change");
        this.editor.off("focus");
        this.editor.off("blur");
        this.editor.off("load");

        this.editor.destroy();
        this.editor = null;
        this.isFocused = false;
      } catch (error) {
        console.error("销毁编辑器失败:", error);
      }
    },

    setMarkdown(value) {
      if (!this.editor) return false;

      try {
        this.editor.setMarkdown(value || "");
        return true;
      } catch (error) {
        console.error("设置 Markdown 内容失败:", error);
        this.hasError = true;
        return false;
      }
    },

    getMarkdown() {
      if (!this.editor) return "";

      try {
        return this.editor.getMarkdown();
      } catch (error) {
        console.error("获取 Markdown 内容失败:", error);
        return "";
      }
    },

    setHtml(value) {
      if (!this.editor) return false;

      try {
        this.editor.setHtml(value || "");
        return true;
      } catch (error) {
        console.error("设置 HTML 内容失败:", error);
        this.hasError = true;
        return false;
      }
    },

    getHtml() {
      if (!this.editor) return "";

      try {
        return this.editor.getHtml();
      } catch (error) {
        console.error("获取 HTML 内容失败:", error);
        return "";
      }
    },

    focus() {
      if (!this.editor || this.disabled) return false;

      try {
        this.editor.focus();
        return true;
      } catch (error) {
        console.error("聚焦编辑器失败:", error);
        return false;
      }
    },

    blur() {
      if (!this.editor) return false;

      try {
        this.editor.blur();
        return true;
      } catch (error) {
        console.error("失焦编辑器失败:", error);
        return false;
      }
    },

    reset() {
      if (!this.editor) return false;

      try {
        this.editor.setMarkdown("");
        this.$emit("input", "");
        this.$emit("change", "");
        this.$emit("reset");
        return true;
      } catch (error) {
        console.error("重置编辑器失败:", error);
        return false;
      }
    },

    insertText(text) {
      if (!this.editor || this.disabled) return false;

      try {
        this.editor.insertText(text);
        return true;
      } catch (error) {
        console.error("插入文本失败:", error);
        return false;
      }
    },
  },
};
</script>

<style scoped>
:deep(.toastui-editor-defaultUI) {
  @apply border-0 rounded-lg overflow-hidden;
}

:deep(.toastui-editor-defaultUI .toastui-editor-toolbar) {
  @apply border-b border-gray-200 bg-gray-50/90 backdrop-blur-sm px-4 py-3;
}

:deep(
    .toastui-editor-defaultUI
      .toastui-editor-toolbar
      .toastui-editor-toolbar-group
  ) {
  @apply border-r border-gray-200 last:border-r-0 px-2 mr-2 last:mr-0;
}

:deep(
    .toastui-editor-defaultUI
      .toastui-editor-toolbar
      .toastui-editor-toolbar-icons
  ) {
  @apply text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md p-2 transition-all duration-150 ease-in-out;
}

:deep(
    .toastui-editor-defaultUI
      .toastui-editor-toolbar
      .toastui-editor-toolbar-icons.active
  ) {
  @apply text-blue-600 bg-blue-100 shadow-sm;
}

:deep(.toastui-editor-main) {
  @apply bg-white;
}

:deep(.toastui-editor-md-container) {
  @apply bg-white;
}

:deep(.toastui-editor-ww-container) {
  @apply bg-white;
}

:deep(.toastui-editor-md-preview) {
  @apply bg-gray-50/50 border-l border-gray-200;
}

:deep(.toastui-editor-md-splitter) {
  @apply bg-gray-300 hover:bg-gray-400 transition-colors duration-150;
}

:deep(.toastui-editor .CodeMirror) {
  @apply text-sm leading-relaxed font-mono;
}

:deep(.toastui-editor .CodeMirror-lines) {
  @apply p-4;
}

:deep(.toastui-editor .CodeMirror-placeholder) {
  @apply text-gray-400 italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.toastui-editor-defaultUI .toastui-editor-toolbar) {
    @apply px-2 py-2;
  }

  :deep(
      .toastui-editor-defaultUI
        .toastui-editor-toolbar
        .toastui-editor-toolbar-group
    ) {
    @apply px-1 mr-1;
  }

  :deep(
      .toastui-editor-defaultUI
        .toastui-editor-toolbar
        .toastui-editor-toolbar-icons
    ) {
    @apply p-1.5 text-sm;
  }
}
</style>
