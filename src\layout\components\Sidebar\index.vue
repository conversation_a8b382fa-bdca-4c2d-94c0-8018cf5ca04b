<template>
  <div
    class="relative h-full overflow-hidden transition-all duration-300 ease-out"
  >
    <div
      class="relative h-full w-full bg-gradient-to-br from-white/95 via-gray-50/92 via-25% via-slate-100/88 via-50% via-slate-200/85 via-75% to-slate-300/82 backdrop-blur-[20px] backdrop-saturate-[180%] rounded-r-3xl shadow-[0_8px_32px_rgba(0,0,0,0.08),0_2px_16px_rgba(0,0,0,0.04),inset_1px_0_0_rgba(255,255,255,0.3),inset_0_1px_0_rgba(255,255,255,0.2)] border-r border-white/20"
    >
      <!-- Logo区域 -->
      <logo v-if="showLogo" :collapse="isCollapse" />

      <!-- 导航菜单区域 -->
      <div
        class="flex-1 overflow-hidden"
        :class="isCollapse ? 'h-[calc(100vh-120px)]' : 'h-[calc(100vh-140px)]'"
      >
        <el-scrollbar
          wrap-class="scrollbar-wrapper overflow-x-hidden pr-2 -mr-2"
          view-class="scrollbar-view"
        >
          <el-menu
            :default-active="activeMenu"
            :collapse="isCollapse"
            background-color="transparent"
            text-color="#374151"
            :unique-opened="false"
            active-text-color="#667eea"
            :collapse-transition="false"
            mode="vertical"
            :popper-append-to-body="true"
            class="modern-el-menu border-0 bg-transparent"
          >
            <sidebar-item
              v-for="route in routes"
              :key="route.path"
              :item="route"
              :base-path="route.path"
            />
          </el-menu>
        </el-scrollbar>
      </div>

      <!-- 底部装饰区域 -->
      <div
        class="absolute bottom-0 left-0 right-0 border-t border-white/20"
        :class="isCollapse ? 'h-12 p-2' : 'h-15 p-4'"
      >
        <div
          class="w-full h-full bg-gradient-to-t from-gray-50/80 to-white/10 backdrop-blur-xl rounded-b-3xl"
          :class="isCollapse ? 'rounded-b-xl' : 'rounded-b-3xl'"
        >
          <div
            v-if="!isCollapse"
            class="flex items-center justify-between h-full px-4"
          >
            <div class="flex items-center">
              <div
                class="w-2 h-2 rounded-full bg-gradient-to-r from-green-500 to-green-600 mr-2 animate-pulse shadow-[0_0_0_4px_rgba(16,185,129,0.2)]"
              ></div>
              <span class="text-xs text-gray-500 font-medium"
                >系统运行正常</span
              >
            </div>
            <div
              class="text-[11px] text-gray-400 font-semibold bg-gradient-to-r from-indigo-500 to-purple-600 bg-clip-text text-transparent"
            >
              v{{ version }}
            </div>
          </div>
          <!-- 收起状态的简化状态显示 -->
          <div v-else class="flex items-center justify-center h-full">
            <div
              class="w-2 h-2 rounded-full bg-gradient-to-r from-green-500 to-green-600 animate-pulse shadow-[0_0_0_4px_rgba(16,185,129,0.2)]"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 侧边栏边框光效 -->
    <div
      class="absolute top-0 -right-0.5 w-0.5 h-full bg-gradient-to-b from-indigo-500/60 via-green-500/40 via-50% to-red-500/30 opacity-60 animate-pulse"
    ></div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";

export default {
  name: "ModernSidebar",
  components: {
    SidebarItem,
    Logo,
  },
  data() {
    return {
      version: "2.1.0",
    };
  },
  computed: {
    ...mapGetters(["sidebar"]),
    routes() {
      return this.$router.options.routes.filter((route) => !route.hidden);
    },
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
};
</script>

<style>
/* Element UI 菜单样式重写 */
.modern-el-menu .el-menu-item {
  background: transparent !important;
  color: #374151 !important;
  display: flex !important;
  align-items: center !important;
  line-height: 1.5 !important;
  margin: 4px 0 !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  min-height: 48px !important;
}

/* 收起状态下的菜单项样式 */
.el-menu--collapse .modern-el-menu .el-menu-item {
  padding: 0 12px !important;
  justify-content: center !important;
  min-height: 44px !important;
  width: auto !important;
}

/* 收起状态下的菜单项内容 */
.el-menu--collapse .modern-el-menu .el-menu-item > div {
  justify-content: center !important;
  width: 100% !important;
}

/* 收起状态下隐藏菜单文字，只显示图标 */
.el-menu--collapse .modern-el-menu .el-menu-item span {
  display: none !important;
}

/* 收起状态下的图标容器 */
.el-menu--collapse .modern-el-menu .el-menu-item .menu-icon-wrapper {
  margin-right: 0 !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 确保收起状态下图标可见 */
.el-menu--collapse .modern-el-menu .el-menu-item .menu-icon-wrapper i,
.el-menu--collapse .modern-el-menu .el-menu-item .menu-icon-wrapper .svg-icon {
  font-size: 18px !important;
  display: block !important;
  color: inherit !important;
}

/* 收起状态下的子菜单标题样式 */
.el-menu--collapse .modern-el-menu .el-submenu .el-submenu__title {
  padding: 0 12px !important;
  justify-content: center !important;
  min-height: 44px !important;
}

/* 收起状态下的子菜单标题内容 */
.el-menu--collapse .modern-el-menu .el-submenu .el-submenu__title > div {
  justify-content: center !important;
  width: 100% !important;
}

/* 收起状态下子菜单隐藏文字 */
.el-menu--collapse .modern-el-menu .el-submenu .el-submenu__title span {
  display: none !important;
}

/* 收起状态下子菜单图标 */
.el-menu--collapse
  .modern-el-menu
  .el-submenu
  .el-submenu__title
  .menu-icon-wrapper {
  margin-right: 0 !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 确保收起状态下子菜单图标可见 */
.el-menu--collapse
  .modern-el-menu
  .el-submenu
  .el-submenu__title
  .menu-icon-wrapper
  i,
.el-menu--collapse
  .modern-el-menu
  .el-submenu
  .el-submenu__title
  .menu-icon-wrapper
  .svg-icon {
  font-size: 18px !important;
  display: block !important;
  color: inherit !important;
}

/* 收起状态下隐藏箭头 */
.el-menu--collapse
  .modern-el-menu
  .el-submenu
  .el-submenu__title
  .el-submenu__icon-arrow {
  display: none !important;
}

/* 收起状态下的整体菜单项布局 */
.el-menu--collapse .modern-el-menu .el-menu-item,
.el-menu--collapse .modern-el-menu .el-submenu .el-submenu__title {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 12px !important;
  min-height: 44px !important;
  position: relative !important;
}

/* 收起状态下确保菜单项内容居中 */
.el-menu--collapse .modern-el-menu .el-menu-item .menu-item-wrapper,
.el-menu--collapse
  .modern-el-menu
  .el-submenu
  .el-submenu__title
  .menu-item-wrapper {
  justify-content: center !important;
  width: 100% !important;
}

/* 收起状态下隐藏所有文字 */
.el-menu--collapse .modern-el-menu .menu-title {
  display: none !important;
}

/* 强制显示收起状态下的图标 */
.el-menu--collapse .modern-el-menu .el-menu-item i[class*="el-icon"],
.el-menu--collapse
  .modern-el-menu
  .el-submenu
  .el-submenu__title
  i[class*="el-icon"] {
  display: inline-block !important;
  font-size: 18px !important;
  color: #374151 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* 收起状态下的菜单项hover效果 */
.el-menu--collapse .modern-el-menu .el-menu-item:hover i[class*="el-icon"],
.el-menu--collapse
  .modern-el-menu
  .el-submenu
  .el-submenu__title:hover
  i[class*="el-icon"] {
  color: #1f2937 !important;
  transform: scale(1.1) !important;
}

.modern-el-menu .el-menu-item:hover {
  background: rgba(255, 255, 255, 0.6) !important;
  color: #1f2937 !important;
  transform: translateX(2px) !important;
}

.modern-el-menu .el-menu-item.is-active {
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.12) 0%,
    rgba(16, 185, 129, 0.08) 100%
  ) !important;
  color: #667eea !important;
  transform: translateX(4px) !important;
  font-weight: 600 !important;
}

.modern-el-menu .el-menu-item.is-active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 2px 2px 0;
}

/* 子菜单样式 */
.modern-el-menu .el-submenu {
  margin: 4px 0 !important;
}

.modern-el-menu .el-submenu .el-submenu__title {
  background: transparent !important;
  color: #374151 !important;
  display: flex !important;
  align-items: center !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  min-height: 48px !important;
}

.modern-el-menu .el-submenu .el-submenu__title:hover {
  background: rgba(255, 255, 255, 0.6) !important;
  color: #1f2937 !important;
  transform: translateX(2px) !important;
}

.modern-el-menu .el-submenu .el-submenu__title .el-submenu__icon-arrow {
  transition: all 0.3s ease !important;
  color: #9ca3af !important;
  margin-left: auto !important;
}

.modern-el-menu
  .el-submenu.is-opened
  .el-submenu__title
  .el-submenu__icon-arrow {
  transform: rotateZ(90deg) !important;
  color: #667eea !important;
}

.modern-el-menu .el-submenu .el-menu {
  background: rgba(248, 250, 252, 0.6) !important;
  border-radius: 8px !important;
  margin: 4px 0 4px 16px !important;
  padding: 4px !important;
  backdrop-filter: blur(8px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.modern-el-menu .el-submenu .el-menu .el-menu-item {
  background: transparent !important;
  color: #6b7280 !important;
  margin: 2px 0 !important;
  padding: 8px 16px !important;
  font-size: 13px !important;
  border-radius: 8px !important;
  min-height: 40px !important;
}

.modern-el-menu .el-submenu .el-menu .el-menu-item:hover {
  background: rgba(255, 255, 255, 0.7) !important;
  color: #374151 !important;
  transform: translateX(1px) !important;
}

.modern-el-menu .el-submenu .el-menu .el-menu-item.is-active {
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.12) 0%,
    rgba(16, 185, 129, 0.08) 100%
  ) !important;
  color: #667eea !important;
  transform: translateX(4px) !important;
}

/* 滚动条样式 */
.modern-el-menu .el-scrollbar .el-scrollbar__bar.is-vertical {
  right: 2px;
  width: 4px;
}

.modern-el-menu
  .el-scrollbar
  .el-scrollbar__bar.is-vertical
  .el-scrollbar__thumb {
  background: linear-gradient(
    180deg,
    rgba(102, 126, 234, 0.6) 0%,
    rgba(16, 185, 129, 0.4) 100%
  );
  border-radius: 2px;
}

.modern-el-menu
  .el-scrollbar
  .el-scrollbar__bar.is-vertical
  .el-scrollbar__thumb:hover {
  background: linear-gradient(
    180deg,
    rgba(102, 126, 234, 0.8) 0%,
    rgba(16, 185, 129, 0.6) 100%
  );
}
</style>
