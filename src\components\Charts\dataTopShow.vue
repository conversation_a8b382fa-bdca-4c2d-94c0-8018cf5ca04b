<template>
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <div @click="clickInfo(0)" class="cursor-pointer">
      <el-card class="hover:shadow-lg transition-shadow duration-300">
        <div class="flex items-center justify-between p-2">
          <div
            class="flex items-center justify-center w-14 h-14 rounded-xl bg-gradient-to-br from-cyan-400 to-cyan-500 text-white shadow-lg"
          >
            <i class="el-icon-user text-2xl" />
          </div>
          <div class="text-right flex-1 ml-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <div class="text-gray-500 text-sm font-medium">今日问答</div>
                <div class="text-gray-800 text-lg font-bold">
                  {{ info.dailyQuestionsNumber }}
                </div>
              </div>
              <div>
                <div class="text-gray-500 text-sm font-medium">总问答数</div>
                <div class="text-gray-800 text-lg font-bold">
                  {{ info.questionsNumber }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <div @click="clickInfo(1)" class="cursor-pointer">
      <el-card class="hover:shadow-lg transition-shadow duration-300">
        <div class="flex items-center justify-between p-2">
          <div
            class="flex items-center justify-center w-14 h-14 rounded-xl bg-gradient-to-br from-blue-400 to-blue-500 text-white shadow-lg"
          >
            <i class="el-icon-thumb text-2xl" />
          </div>
          <div class="text-right flex-1 ml-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <div class="text-gray-500 text-sm font-medium">用户总数</div>
                <div class="text-gray-800 text-lg font-bold">
                  {{ info.usersNumber }}
                </div>
              </div>
              <div>
                <div class="text-gray-500 text-sm font-medium">今日访问</div>
                <div class="text-gray-800 text-lg font-bold">
                  {{ info.visitsNumber }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <div @click="clickInfo(2)" class="cursor-pointer">
      <el-card class="hover:shadow-lg transition-shadow duration-300">
        <div class="flex items-center justify-between p-2">
          <div
            class="flex items-center justify-center w-14 h-14 rounded-xl bg-gradient-to-br from-yellow-400 to-yellow-500 text-white shadow-lg"
          >
            <i class="el-icon-medal text-2xl" />
          </div>
          <div class="text-right flex-1 ml-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <div class="text-gray-500 text-sm font-medium">会员总数</div>
                <div class="text-gray-800 text-lg font-bold">
                  {{ info.allVipNumbers }}
                </div>
              </div>
              <div>
                <div class="text-gray-500 text-sm font-medium">超级会员</div>
                <div class="text-gray-800 text-lg font-bold">
                  {{ info.allSuperVipNumbers }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getKanBan } from "@/api/dashboard";

export default {
  name: "DataTopShow",
  data() {
    return {
      info: {
        usersNumber: "加载中...",
        visitsNumber: "加载中...",
        questionsNumber: "加载中...",
        allVipNumbers: "加载中...",
        allSuperVipNumbers: "加载中...",
        dailyQuestionsNumber: "加载中...",
      },
    };
  },
  mounted() {
    this.getKanBan();
  },
  methods: {
    clickInfo(item) {
      console.log(item);
      this.$emit("checkEchart", item);
    },
    getKanBan() {
      getKanBan()
        .then((res) => {
          if (res.code !== 200) {
            return;
          }
          this.info = res.data;
          console.log(this.info);
        })
        .catch((error) => {
          console.error("网络错误:", error);
        });
    },
  },
};
</script>
