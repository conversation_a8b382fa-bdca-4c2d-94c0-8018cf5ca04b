{"name": "vue-admin-template", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve --open", "dev:fast": "cross-env NODE_ENV=development vue-cli-service serve --open", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix", "clean": "rimraf node_modules/.cache && rimraf dist", "analyze": "vue-cli-service build --report"}, "dependencies": {"@tailwindcss/postcss7-compat": "^2.2.17", "@toast-ui/editor": "^3.2.2", "@toast-ui/editor-plugin-color-syntax": "^3.1.0", "axios": "0.18.1", "codemirror": "^5.45.0", "core-js": "3.6.5", "echarts": "^4.2.1", "element-ui": "2.13.2", "file-saver": "^2.0.5", "js-cookie": "2.2.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "nvm": "^0.0.4", "path-to-regexp": "2.4.0", "vue": "2.6.10", "vue-json-excel": "^0.3.0", "vue-router": "3.0.6", "vuex": "3.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-service": "4.4.4", "autoprefixer": "^9.8.8", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "postcss": "^7.0.39", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}