<template>
  <el-card class="ml-2 overflow-hidden">
    <div slot="header" class="relative h-56 overflow-hidden p-0">
      <img
        src="https://wpimg.wallstcn.com/e7d23d71-cf19-4b90-a1cc-f56af8c0903d.png"
        class="w-full h-full object-cover transition-transform duration-200 hover:scale-110 hover:contrast-125"
        alt="Header Image"
      />
    </div>
    <div class="relative pt-8">
      <pan-thumb
        :image="avatar"
        class="absolute -top-9 left-0 z-10 w-16 h-16 border-4 border-white bg-white rounded-full shadow-none"
      />
      <mallki
        class="absolute top-0 right-0 text-xl font-bold text-gray-800 hidden xl:block"
        text="vue-element-admin"
      />
      <div class="pt-6 space-y-3">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">Vue</span>
          <div class="flex-1 ml-4">
            <el-progress :percentage="70" />
          </div>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">JavaScript</span>
          <div class="flex-1 ml-4">
            <el-progress :percentage="18" />
          </div>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">CSS</span>
          <div class="flex-1 ml-4">
            <el-progress :percentage="12" />
          </div>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600">ESLint</span>
          <div class="flex-1 ml-4">
            <el-progress :percentage="100" status="success" />
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { mapGetters } from "vuex";
import PanThumb from "@/components/PanThumb";
import Mallki from "@/components/TextHoverEffect/Mallki";

export default {
  components: { PanThumb, Mallki },

  filters: {
    statusFilter(status) {
      const statusMap = {
        success: "success",
        pending: "danger",
      };
      return statusMap[status];
    },
  },
  data() {
    return {
      statisticsData: {
        article_count: 1024,
        pageviews_count: 1024,
      },
    };
  },
  computed: {
    ...mapGetters(["name", "avatar", "roles"]),
  },
};
</script>

<style>
/* Override Element UI header padding */
.el-card__header {
  padding: 0px !important;
}

/* Deep selector for pan-thumb component */
.pan-info {
  box-shadow: none !important;
}
</style>
