<template>
  <div
    ref="toastuiEditor"
    class="w-full h-full min-h-[200px] border border-gray-200 rounded-lg shadow-sm bg-white overflow-hidden focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all duration-200"
    :class="{
      'opacity-60 pointer-events-none': disabled,
      'border-red-300 focus-within:ring-red-500 focus-within:border-red-500':
        hasError,
    }"
  />
</template>

<script>
import Editor from "@toast-ui/editor";
import { optionsMixin } from "./mixin/option";

export default {
  name: "ToastuiEditor",
  mixins: [optionsMixin],
  props: {
    previewStyle: {
      type: String,
      default: "vertical",
      validator: (value) => ["tab", "vertical"].includes(value),
    },
    height: {
      type: String,
      default: "300px",
    },
    initialEditType: {
      type: String,
      default: "markdown",
      validator: (value) => ["markdown", "wysiwyg"].includes(value),
    },
    initialValue: {
      type: String,
      default: "",
    },
    options: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: "请输入内容...",
    },
    theme: {
      type: String,
      default: "default",
      validator: (value) => ["default", "dark"].includes(value),
    },
  },
  data() {
    return {
      hasError: false,
      isLoading: false,
    };
  },
  computed: {
    mergedOptions() {
      return {
        ...this.computedOptions,
        placeholder: this.placeholder,
        theme: this.theme,
      };
    },
  },
  watch: {
    previewStyle(newValue) {
      if (this.editor) {
        try {
          this.editor.changePreviewStyle(newValue);
        } catch (error) {
          console.error("切换预览样式失败:", error);
          this.hasError = true;
        }
      }
    },
    height(newValue) {
      if (this.editor) {
        try {
          this.editor.height(newValue);
        } catch (error) {
          console.error("设置高度失败:", error);
          this.hasError = true;
        }
      }
    },
    disabled(newValue) {
      if (this.editor) {
        const element = this.$refs.toastuiEditor;
        if (element) {
          element.style.pointerEvents = newValue ? "none" : "auto";
        }
      }
    },
    theme(newValue) {
      // 主题变化时重新初始化编辑器
      if (this.editor) {
        this.reinitializeEditor();
      }
    },
  },
  mounted() {
    this.initializeEditor();
  },
  beforeDestroy() {
    this.cleanupEditor();
  },
  methods: {
    initializeEditor() {
      try {
        this.isLoading = true;
        this.hasError = false;

        const options = {
          ...this.mergedOptions,
          el: this.$refs.toastuiEditor,
        };

        this.editor = new Editor(options);

        // 监听编辑器事件
        this.setupEditorEvents();

        // 设置初始状态
        if (this.disabled) {
          this.$refs.toastuiEditor.style.pointerEvents = "none";
        }

        this.$emit("ready", this.editor);
      } catch (error) {
        console.error("初始化编辑器失败:", error);
        this.hasError = true;
        this.$emit("error", error);
      } finally {
        this.isLoading = false;
      }
    },

    setupEditorEvents() {
      if (!this.editor) return;

      // 内容变化事件
      this.editor.on("change", () => {
        const content = this.getMarkdown();
        this.$emit("change", content);
        this.$emit("input", content);
      });

      // 焦点事件
      this.editor.on("focus", () => {
        this.hasError = false;
        this.$emit("focus");
      });

      this.editor.on("blur", () => {
        this.$emit("blur");
      });

      // 加载完成事件
      this.editor.on("load", () => {
        this.$emit("load", this.editor);
      });
    },

    reinitializeEditor() {
      const currentContent = this.getMarkdown();
      this.cleanupEditor();
      this.initializeEditor();
      if (currentContent) {
        this.$nextTick(() => {
          this.setMarkdown(currentContent);
        });
      }
    },

    cleanupEditor() {
      if (this.editor) {
        try {
          // 移除所有事件监听器
          this.editor.off("change");
          this.editor.off("focus");
          this.editor.off("blur");
          this.editor.off("load");

          this.editor.destroy();
          this.editor = null;
        } catch (error) {
          console.error("清理编辑器失败:", error);
        }
      }
    },

    getRootElement() {
      return this.$refs.toastuiEditor;
    },

    // 扩展的编辑器方法
    getMarkdown() {
      if (!this.editor) return "";
      try {
        return this.editor.getMarkdown();
      } catch (error) {
        console.error("获取 Markdown 内容失败:", error);
        return "";
      }
    },

    setMarkdown(content) {
      if (!this.editor) return;
      try {
        this.editor.setMarkdown(content || "");
      } catch (error) {
        console.error("设置 Markdown 内容失败:", error);
        this.hasError = true;
      }
    },

    getHtml() {
      if (!this.editor) return "";
      try {
        return this.editor.getHtml();
      } catch (error) {
        console.error("获取 HTML 内容失败:", error);
        return "";
      }
    },

    setHtml(content) {
      if (!this.editor) return;
      try {
        this.editor.setHtml(content || "");
      } catch (error) {
        console.error("设置 HTML 内容失败:", error);
        this.hasError = true;
      }
    },

    focus() {
      if (!this.editor) return;
      try {
        this.editor.focus();
      } catch (error) {
        console.error("聚焦编辑器失败:", error);
      }
    },

    blur() {
      if (!this.editor) return;
      try {
        this.editor.blur();
      } catch (error) {
        console.error("失焦编辑器失败:", error);
      }
    },

    reset() {
      this.setMarkdown("");
      this.hasError = false;
      this.$emit("reset");
    },

    insertText(text) {
      if (!this.editor) return;
      try {
        this.editor.insertText(text);
      } catch (error) {
        console.error("插入文本失败:", error);
        this.hasError = true;
      }
    },
  },
};
</script>

<style scoped>
/* Toast UI 编辑器自定义样式 */
:deep(.toastui-editor-defaultUI) {
  @apply border-0 rounded-lg overflow-hidden;
}

:deep(.toastui-editor-defaultUI .toastui-editor-toolbar) {
  @apply border-b border-gray-200 bg-gray-50/80 backdrop-blur-sm px-4 py-2;
}

:deep(
    .toastui-editor-defaultUI
      .toastui-editor-toolbar
      .toastui-editor-toolbar-group
  ) {
  @apply border-r border-gray-200 last:border-r-0 px-2;
}

:deep(
    .toastui-editor-defaultUI
      .toastui-editor-toolbar
      .toastui-editor-toolbar-icons
  ) {
  @apply text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded p-1 transition-colors duration-150;
}

:deep(
    .toastui-editor-defaultUI
      .toastui-editor-toolbar
      .toastui-editor-toolbar-icons.active
  ) {
  @apply text-blue-600 bg-blue-100;
}

:deep(.toastui-editor-main) {
  @apply bg-white;
}

:deep(.toastui-editor-md-container) {
  @apply bg-white;
}

:deep(.toastui-editor-ww-container) {
  @apply bg-white;
}

:deep(.toastui-editor-md-preview) {
  @apply bg-gray-50/50 border-l border-gray-200;
}

:deep(.toastui-editor-md-splitter) {
  @apply bg-gray-300 hover:bg-gray-400 transition-colors duration-150;
}

:deep(.toastui-editor .CodeMirror) {
  @apply text-sm leading-relaxed;
}

:deep(.toastui-editor .CodeMirror-lines) {
  @apply p-4;
}

:deep(.toastui-editor .CodeMirror-placeholder) {
  @apply text-gray-400;
}

/* 暗色主题支持 */
:deep(.theme-dark .toastui-editor-defaultUI) {
  @apply bg-gray-800 border-gray-600;
}

:deep(.theme-dark .toastui-editor-defaultUI .toastui-editor-toolbar) {
  @apply bg-gray-700 border-gray-600;
}

:deep(.theme-dark .toastui-editor-main) {
  @apply bg-gray-800;
}

:deep(.theme-dark .toastui-editor-md-container) {
  @apply bg-gray-800;
}

:deep(.theme-dark .toastui-editor-ww-container) {
  @apply bg-gray-800;
}

:deep(.theme-dark .toastui-editor-md-preview) {
  @apply bg-gray-700 border-gray-600;
}
</style>
