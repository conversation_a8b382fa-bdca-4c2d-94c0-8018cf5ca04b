// 现代化侧边栏配色方案
$menuText: #374151;
$menuActiveText: #667eea;
$subMenuActiveText: #f4f4f5;

// 新的侧边栏样式变量
$menuBg: transparent;
$menuHover: rgba(255, 255, 255, 0.6);
$menuActive: linear-gradient(
  135deg,
  rgba(102, 126, 234, 0.15) 0%,
  rgba(16, 185, 129, 0.1) 100%
);

$subMenuBg: rgba(248, 250, 252, 0.6);
$subMenuHover: rgba(255, 255, 255, 0.8);

$sideBarWidth: 240px;

// 现代化主题色彩系统
$primary-color: #667eea;
$primary-light: rgba(102, 126, 234, 0.1);
$primary-dark: #5a67d8;

$secondary-color: #10b981;
$secondary-light: rgba(16, 185, 129, 0.1);
$secondary-dark: #059669;

$accent-color: #f59e0b;
$accent-light: rgba(245, 158, 11, 0.1);
$accent-dark: #d97706;

$success-color: #10b981;
$warning-color: #f59e0b;
$danger-color: #ef4444;
$info-color: #3b82f6;

// 中性色系
$text-primary: #1f2937;
$text-secondary: #374151;
$text-tertiary: #6b7280;
$text-quaternary: #9ca3af;

// 现代化背景和表面
$bg-primary: #f8fafc;
$bg-secondary: #f1f5f9;
$bg-tertiary: #e2e8f0;

$surface-primary: rgba(255, 255, 255, 0.95);
$surface-secondary: rgba(248, 250, 252, 0.9);
$surface-tertiary: rgba(241, 245, 249, 0.85);

// 毛玻璃效果变量
$glass-backdrop: blur(20px) saturate(180%);
$glass-light: blur(16px) saturate(160%);
$glass-medium: blur(12px) saturate(140%);

// 现代化阴影系统
$shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
$shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
$shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

// 现代化渐变
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
$gradient-accent: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
$gradient-glass: linear-gradient(
  145deg,
  rgba(255, 255, 255, 0.95) 0%,
  rgba(248, 250, 252, 0.92) 25%,
  rgba(241, 245, 249, 0.88) 50%,
  rgba(226, 232, 240, 0.85) 75%,
  rgba(203, 213, 225, 0.82) 100%
);

// 边框和分隔线
$border-color: rgba(229, 231, 235, 0.3);
$border-light: rgba(255, 255, 255, 0.2);
$border-dark: rgba(0, 0, 0, 0.1);

// 现代化圆角系统
$radius-xs: 4px;
$radius-sm: 6px;
$radius-md: 8px;
$radius-lg: 12px;
$radius-xl: 16px;
$radius-2xl: 24px;

// 动画和过渡
$transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
$transition-base: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
$transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

// 现代化间距系统
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 24px;
$spacing-2xl: 32px;

// 字体系统
$font-family-sans: "SF Pro Display", -apple-system, BlinkMacSystemFont,
  "Segoe UI", Roboto, sans-serif;
$font-size-xs: 11px;
$font-size-sm: 12px;
$font-size-base: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-2xl: 20px;

// Z-index 层级
$z-dropdown: 1000;
$z-sidebar: 1001;
$z-modal: 1050;
$z-tooltip: 1060;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  // 原有变量保持兼容性
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;

  // 新的主题色彩
  primaryColor: $primary-color;
  primaryLight: $primary-light;
  primaryDark: $primary-dark;
  secondaryColor: $secondary-color;
  secondaryLight: $secondary-light;
  secondaryDark: $secondary-dark;
  accentColor: $accent-color;
  accentLight: $accent-light;
  accentDark: $accent-dark;

  successColor: $success-color;
  warningColor: $warning-color;
  dangerColor: $danger-color;
  infoColor: $info-color;

  // 文本颜色
  textPrimary: $text-primary;
  textSecondary: $text-secondary;
  textTertiary: $text-tertiary;
  textQuaternary: $text-quaternary;

  // 背景色
  bgPrimary: $bg-primary;
  bgSecondary: $bg-secondary;
  bgTertiary: $bg-tertiary;

  surfacePrimary: $surface-primary;
  surfaceSecondary: $surface-secondary;
  surfaceTertiary: $surface-tertiary;

  // 阴影
  shadowXs: $shadow-xs;
  shadowSm: $shadow-sm;
  shadowMd: $shadow-md;
  shadowLg: $shadow-lg;
  shadowXl: $shadow-xl;
  shadow2xl: $shadow-2xl;

  // 边框
  borderColor: $border-color;
  borderLight: $border-light;
  borderDark: $border-dark;

  // 圆角
  radiusXs: $radius-xs;
  radiusSm: $radius-sm;
  radiusMd: $radius-md;
  radiusLg: $radius-lg;
  radiusXl: $radius-xl;
  radius2xl: $radius-2xl;

  // 动画
  transitionFast: $transition-fast;
  transitionBase: $transition-base;
  transitionSlow: $transition-slow;

  // 间距
  spacingXs: $spacing-xs;
  spacingSm: $spacing-sm;
  spacingMd: $spacing-md;
  spacingLg: $spacing-lg;
  spacingXl: $spacing-xl;
  spacing2xl: $spacing-2xl;

  // 字体
  fontFamilySans: $font-family-sans;
  fontSizeXs: $font-size-xs;
  fontSizeSm: $font-size-sm;
  fontSizeBase: $font-size-base;
  fontSizeLg: $font-size-lg;
  fontSizeXl: $font-size-xl;
  fontSize2xl: $font-size-2xl;
}
