<template>
  <el-row :gutter="40" class="mt-4">
    <el-col :xs="12" :sm="12" :lg="6" class="mb-8">
      <div
        class="h-28 cursor-pointer text-xs relative overflow-hidden text-gray-600 bg-white shadow-md rounded-lg hover:shadow-lg transition-shadow duration-300"
        @click="handleSetLineChartData('newVisitis')"
      >
        <div class="flex items-center justify-between p-4 h-full">
          <div
            class="flex items-center justify-center w-16 h-16 rounded-lg text-teal-500 bg-teal-50 transition-colors duration-300 hover:bg-teal-500 hover:text-white"
          >
            <svg-icon icon-class="peoples" class="text-4xl" />
          </div>
          <div class="text-right hidden sm:block">
            <div class="text-gray-500 text-sm font-medium mb-1">New Visits</div>
            <count-to
              :start-val="0"
              :end-val="102400"
              :duration="2600"
              class="text-2xl font-bold text-gray-800"
            />
          </div>
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="mb-8">
      <div
        class="h-28 cursor-pointer text-xs relative overflow-hidden text-gray-600 bg-white shadow-md rounded-lg hover:shadow-lg transition-shadow duration-300"
        @click="handleSetLineChartData('messages')"
      >
        <div class="flex items-center justify-between p-4 h-full">
          <div
            class="flex items-center justify-center w-16 h-16 rounded-lg text-blue-500 bg-blue-50 transition-colors duration-300 hover:bg-blue-500 hover:text-white"
          >
            <svg-icon icon-class="message" class="text-4xl" />
          </div>
          <div class="text-right hidden sm:block">
            <div class="text-gray-500 text-sm font-medium mb-1">Messages</div>
            <count-to
              :start-val="0"
              :end-val="81212"
              :duration="3000"
              class="text-2xl font-bold text-gray-800"
            />
          </div>
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="mb-8">
      <div
        class="h-28 cursor-pointer text-xs relative overflow-hidden text-gray-600 bg-white shadow-md rounded-lg hover:shadow-lg transition-shadow duration-300"
        @click="handleSetLineChartData('purchases')"
      >
        <div class="flex items-center justify-between p-4 h-full">
          <div
            class="flex items-center justify-center w-16 h-16 rounded-lg text-red-500 bg-red-50 transition-colors duration-300 hover:bg-red-500 hover:text-white"
          >
            <svg-icon icon-class="money" class="text-4xl" />
          </div>
          <div class="text-right hidden sm:block">
            <div class="text-gray-500 text-sm font-medium mb-1">Purchases</div>
            <count-to
              :start-val="0"
              :end-val="9280"
              :duration="3200"
              class="text-2xl font-bold text-gray-800"
            />
          </div>
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="mb-8">
      <div
        class="h-28 cursor-pointer text-xs relative overflow-hidden text-gray-600 bg-white shadow-md rounded-lg hover:shadow-lg transition-shadow duration-300"
        @click="handleSetLineChartData('shoppings')"
      >
        <div class="flex items-center justify-between p-4 h-full">
          <div
            class="flex items-center justify-center w-16 h-16 rounded-lg text-green-500 bg-green-50 transition-colors duration-300 hover:bg-green-500 hover:text-white"
          >
            <svg-icon icon-class="shopping" class="text-4xl" />
          </div>
          <div class="text-right hidden sm:block">
            <div class="text-gray-500 text-sm font-medium mb-1">Shoppings</div>
            <count-to
              :start-val="0"
              :end-val="13600"
              :duration="3600"
              class="text-2xl font-bold text-gray-800"
            />
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import CountTo from "vue-count-to";

export default {
  components: {
    CountTo,
  },
  methods: {
    handleSetLineChartData(type) {
      this.$emit("handleSetLineChartData", type);
    },
  },
};
</script>
