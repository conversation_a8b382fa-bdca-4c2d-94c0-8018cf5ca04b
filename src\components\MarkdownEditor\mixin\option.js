// Toast UI Editor 支持的事件列表
const editorEvents = [
  "load",
  "change",
  "caretChange",
  "focus",
  "blur",
  "keydown",
  "keyup",
  "beforePreviewRender",
  "beforeConvertWysiwygToMarkdown",
];

// 编辑器默认配置值
const defaultValueMap = {
  initialEditType: "markdown",
  initialValue: "",
  height: "300px",
  previewStyle: "vertical",
  language: "zh-CN",
  toolbarItems: [
    ["heading", "bold", "italic", "strike"],
    ["hr", "quote"],
    ["ul", "ol", "task"],
    ["table", "image", "link"],
    ["code", "codeblock"],
    ["scrollSync"],
  ],
};

// 错误消息映射
const errorMessages = {
  load: "编辑器加载失败",
  change: "内容变更失败",
  focus: "编辑器聚焦失败",
  blur: "编辑器失焦失败",
  destroy: "编辑器销毁失败",
  method_not_found: "编辑器方法不存在",
  invalid_content: "无效的内容格式",
};

export const optionsMixin = {
  data() {
    const eventOptions = {};
    const self = this;

    // 为每个事件创建处理器
    editorEvents.forEach((event) => {
      eventOptions[event] = (...args) => {
        try {
          self.$emit(event, ...args);
        } catch (error) {
          console.error(
            `${errorMessages[event] || `事件 ${event} 处理失败`}:`,
            error
          );
          self.$emit("error", {
            type: event,
            message: errorMessages[event] || `事件 ${event} 处理失败`,
            originalError: error,
          });
        }
      };
    });

    // 合并配置选项
    const options = {
      ...this.options,
      initialEditType: this.initialEditType,
      initialValue: this.initialValue,
      height: this.height,
      previewStyle: this.previewStyle,
      events: eventOptions,
    };

    // 应用默认值
    Object.keys(defaultValueMap).forEach((key) => {
      if (!options[key]) {
        options[key] = defaultValueMap[key];
      }
    });

    return {
      editor: null,
      computedOptions: options,
      isEditorReady: false,
      lastError: null,
    };
  },

  computed: {
    // 编辑器是否可用
    isEditorAvailable() {
      return this.editor && this.isEditorReady;
    },

    // 编辑器配置摘要（用于调试）
    editorConfig() {
      return {
        height: this.computedOptions.height,
        editType: this.computedOptions.initialEditType,
        previewStyle: this.computedOptions.previewStyle,
        language: this.computedOptions.language || "zh-CN",
      };
    },
  },

  watch: {
    // 监听配置变化
    options: {
      handler(newOptions) {
        if (this.isEditorAvailable) {
          // 部分配置变化时重新初始化编辑器
          const criticalKeys = ["height", "previewStyle", "toolbarItems"];
          const hasChanged = criticalKeys.some(
            (key) => newOptions[key] !== this.computedOptions[key]
          );

          if (hasChanged) {
            this.reinitializeEditor();
          }
        }
      },
      deep: true,
    },
  },

  methods: {
    /**
     * 安全调用编辑器方法
     * @param {string} methodName - 方法名
     * @param {...any} args - 方法参数
     * @returns {any} 方法返回值
     */
    invoke(methodName, ...args) {
      if (!this.isEditorAvailable) {
        console.warn("编辑器未就绪，无法调用方法:", methodName);
        return null;
      }

      try {
        if (!this.editor[methodName]) {
          throw new Error(`${errorMessages.method_not_found}: ${methodName}`);
        }

        const result = this.editor[methodName](...args);
        this.lastError = null; // 清除之前的错误
        return result;
      } catch (error) {
        console.error(`调用编辑器方法 ${methodName} 失败:`, error);
        this.lastError = {
          method: methodName,
          message: error.message,
          timestamp: Date.now(),
        };

        this.$emit("error", {
          type: "method_call",
          method: methodName,
          message: `调用方法 ${methodName} 失败: ${error.message}`,
          originalError: error,
        });

        return null;
      }
    },

    /**
     * 重新初始化编辑器
     */
    reinitializeEditor() {
      if (!this.editor) return;

      try {
        const currentContent = this.editor.getMarkdown();
        this.destroyEditor();

        this.$nextTick(() => {
          this.initEditor();
          if (currentContent) {
            this.editor.setMarkdown(currentContent);
          }
        });
      } catch (error) {
        console.error("重新初始化编辑器失败:", error);
        this.$emit("error", {
          type: "reinitialize",
          message: "重新初始化编辑器失败",
          originalError: error,
        });
      }
    },

    /**
     * 销毁编辑器
     */
    destroyEditor() {
      if (!this.editor) return;

      try {
        this.isEditorReady = false;

        // 移除所有事件监听器
        editorEvents.forEach((event) => {
          this.editor.off(event);
        });

        // 销毁编辑器实例
        this.editor.destroy();
        this.editor = null;

        this.$emit("destroyed");
      } catch (error) {
        console.error(`${errorMessages.destroy}:`, error);
        this.$emit("error", {
          type: "destroy",
          message: errorMessages.destroy,
          originalError: error,
        });
      }
    },

    /**
     * 验证内容格式
     * @param {string} content - 内容
     * @param {string} type - 内容类型 ('markdown' | 'html')
     * @returns {boolean} 是否有效
     */
    validateContent(content, type = "markdown") {
      if (typeof content !== "string") {
        return false;
      }

      // 基本的内容验证
      if (type === "html") {
        // 简单的 HTML 验证
        return content.trim().length === 0 || /<[^>]*>/.test(content);
      }

      // Markdown 内容总是有效的（即使是空字符串）
      return true;
    },

    /**
     * 获取编辑器状态信息
     * @returns {object} 状态信息
     */
    getEditorStatus() {
      return {
        isReady: this.isEditorReady,
        hasEditor: !!this.editor,
        lastError: this.lastError,
        config: this.editorConfig,
      };
    },
  },

  beforeDestroy() {
    this.destroyEditor();
  },
};
