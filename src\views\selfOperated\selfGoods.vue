﻿<template>
  <div class="app-container">
    <!-- 操作区域 -->
    <div class="action-section">
      <div class="action-card">
        <div class="action-header">
          <i class="el-icon-goods action-icon" />
          <span class="action-title">商品管理</span>
        </div>
        <div class="action-buttons">
          <el-button
            type="primary"
            icon="el-icon-plus"
            class="action-btn add-btn"
            @click="addGoods"
          >
            添加商品信息
          </el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-card">
        <div class="table-header">
          <div class="table-title">
            <i class="el-icon-s-goods" />
            <span>自营商品列表</span>
          </div>
        </div>

        <el-table
          :data="goodsList"
          stripe
          style="width: 100%"
          class="modern-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#374151',
            fontWeight: '600',
          }"
          :row-style="{ height: '70px' }"
        >
          <el-table-column
            prop="goodsId"
            width="100"
            label="商品ID"
            align="center"
          >
            <template slot-scope="scope">
              <span class="goods-id">{{ scope.row.goodsId }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="goodsName"
            label="商品名"
            align="center"
            min-width="150"
          >
            <template slot-scope="scope">
              <div class="goods-name">
                <i class="el-icon-box goods-icon" />
                <span>{{ scope.row.goodsName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="商品封面" align="center" width="120">
            <template v-slot="scope">
              <div class="image-preview">
                <el-image
                  style="width: 60px; height: 60px; border-radius: 8px"
                  :src="
                    Array.isArray(scope.row.goodsCover) &&
                    scope.row.goodsCover.length > 0
                      ? scope.row.goodsCover[0]
                      : ''
                  "
                  :preview-src-list="
                    Array.isArray(scope.row.goodsCover)
                      ? scope.row.goodsCover
                      : []
                  "
                  fit="cover"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline" />
                  </div>
                </el-image>
                <span
                  v-if="
                    Array.isArray(scope.row.goodsCover) &&
                    scope.row.goodsCover.length > 1
                  "
                  class="image-count"
                >
                  +{{ scope.row.goodsCover.length - 1 }}
                </span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="详情图片" width="120" align="center">
            <template v-slot="scope">
              <div class="image-preview">
                <el-image
                  style="width: 60px; height: 60px; border-radius: 8px"
                  :src="
                    Array.isArray(scope.row.goodsPic) &&
                    scope.row.goodsPic.length > 0
                      ? scope.row.goodsPic[0]
                      : ''
                  "
                  :preview-src-list="
                    Array.isArray(scope.row.goodsPic) ? scope.row.goodsPic : []
                  "
                  fit="cover"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline" />
                  </div>
                </el-image>
                <span
                  v-if="
                    Array.isArray(scope.row.goodsPic) &&
                    scope.row.goodsPic.length > 1
                  "
                  class="image-count"
                >
                  +{{ scope.row.goodsPic.length - 1 }}
                </span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="goodsTotalPrice"
            label="商品总价格"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <span class="price-text total-price"
                >¥{{ scope.row.goodsTotalPrice }}</span
              >
            </template>
          </el-table-column>

          <el-table-column
            prop="goodsRealPay"
            width="130"
            label="实付价格"
            align="center"
          >
            <template slot-scope="scope">
              <span class="price-text real-price"
                >¥{{ scope.row.goodsRealPay }}</span
              >
            </template>
          </el-table-column>

          <el-table-column
            prop="deductionPoints"
            label="抵扣积分"
            align="center"
            width="100"
          >
            <template slot-scope="scope">
              <span class="points-text">{{ scope.row.deductionPoints }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="freight"
            label="运费"
            align="center"
            width="80"
          >
            <template slot-scope="scope">
              <span class="freight-text">¥{{ scope.row.freight }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="addTime"
            label="添加时间"
            align="center"
            width="160"
          >
            <template slot-scope="scope">
              <span class="time-text">{{ scope.row.addTime }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="uploadTime"
            label="上架时间"
            align="center"
            width="160"
          >
            <template slot-scope="scope">
              <span class="time-text">{{ scope.row.uploadTime }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="goodsParameter"
            label="商品参数"
            align="center"
            min-width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span class="parameter-text">{{ scope.row.goodsParameter }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="serviceDescription"
            label="服务说明"
            align="center"
            min-width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span class="description-text">{{
                scope.row.serviceDescription
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            width="180"
            fixed="right"
            align="center"
          >
            <template v-slot="scope">
              <el-button
                type="primary"
                size="mini"
                class="operation-btn edit-btn"
                @click="editGoods(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="mini"
                class="operation-btn delete-btn"
                @click="deleteGoods(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <el-pagination
        background
        :current-page="currentPage"
        :page-size="pagesize"
        :page-sizes="[5, 10, 20, 30, 40]"
        :total="totalnum"
        layout="total, prev, pager, next, jumper, sizes"
        class="modern-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑商品对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="50%">
      <el-form
        ref="goodsForm"
        :model="goodsForm"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label="商品名称" prop="goodsName">
          <el-input v-model="goodsForm.goodsName" />
        </el-form-item>
        <el-form-item label="商品封面" prop="goodsCover">
          <el-upload
            class="avatar-uploader"
            action="#"
            :auto-upload="false"
            :show-file-list="true"
            :file-list="coverFileList"
            multiple
            list-type="picture-card"
            :on-change="handleCoverChange"
            :on-remove="handleCoverRemove"
          >
            <i class="el-icon-plus" />
          </el-upload>
        </el-form-item>
        <el-form-item label="商品详情图片" prop="goodsPic">
          <el-upload
            class="avatar-uploader"
            action="#"
            :auto-upload="false"
            :show-file-list="true"
            :file-list="picFileList"
            multiple
            list-type="picture-card"
            :on-change="handlePicChange"
            :on-remove="handlePicRemove"
          >
            <i class="el-icon-plus" />
          </el-upload>
        </el-form-item>
        <el-form-item label="商品总价格" prop="goodsTotalPrice">
          <el-input-number
            v-model="goodsForm.goodsTotalPrice"
            :precision="2"
            :step="0.1"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="实付商品价格" prop="goodsRealPay">
          <el-input-number
            v-model="goodsForm.goodsRealPay"
            :precision="2"
            :step="0.1"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="抵扣积分" prop="deductionPoints">
          <el-input-number
            v-model="goodsForm.deductionPoints"
            :precision="2"
            :min="0"
            :step="0.1"
          />
        </el-form-item>
        <el-form-item label="运费" prop="freight">
          <el-input-number
            v-model="goodsForm.freight"
            :precision="2"
            :step="0.1"
            :min="0"
          />
        </el-form-item>

        <el-form-item label="上架时间" prop="uploadTime">
          <el-date-picker
            v-model="goodsForm.uploadTime"
            type="datetime"
            placeholder="选择上架时间"
            :picker-options="pickerOptions"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="商品参数" prop="freight">
          <el-input
            v-model="goodsForm.goodsParameter"
            type="textarea"
            resize="none"
            :rows="4"
            :precision="2"
            :step="0.1"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="服务说明" prop="freight">
          <el-input
            v-model="goodsForm.serviceDescription"
            type="textarea"
            resize="none"
            :rows="4"
            :precision="2"
            :step="0.1"
            :min="0"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getGoodsList,
  updateGoodsInfo,
  addGoods,
  uploadImage,
} from "@/api/selfOperated";

export default {
  data() {
    return {
      goodsName: "",
      dateRange: "",
      goodsList: [],
      total: 0,
      currentPage: 1,
      pagesize: 10,
      totalnum: 0,
      dialogVisible: false,
      dialogTitle: "添加商品",
      coverFileList: [], // 商品封面文件列表
      picFileList: [], // 商品详情图片文件列表
      goodsForm: {
        goodsId: "",
        goodsName: "",
        goodsCover: [],
        goodsPic: [],
        goodsTotalPrice: 0,
        goodsRealPay: 0,
        deductionPoints: 0,
        freight: 0,
        uploadTime: "",
      },
      rules: {
        goodsName: [
          {
            required: true,
            message: "请输入商品名称",
            trigger: "blur",
          },
        ],
        goodsCover: [
          {
            required: true,
            message: "请上传商品封面",
            trigger: "change",
          },
        ],
        goodsPic: [
          {
            required: true,
            message: "请上传商品详情图片",
            trigger: "change",
          },
        ],
        goodsTotalPrice: [
          {
            required: true,
            message: "请输入商品总价格",
            trigger: "blur",
          },
        ],
        // goodsRealPay: [
        // 	{ required: true, message: '请输入实付商品价格', trigger: 'blur' }
        // ],
        uploadTime: [
          {
            required: true,
            message: "请输入上架时间",
            trigger: "blur",
          },
        ],
      },
      pickerOptions: {
        disabledDate(time) {
          // 不限制日期选择范围，允许选择未来日期
          return false;
        },
      },
      detailVisible: false,
      detailForm: {},
    };
  },
  mounted() {
    this.fetchGoodsList();
  },
  methods: {
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 日期变化处理
    handleDateChange(val) {
      console.log("日期变化:", val);
    },
    // 获取商品列表
    fetchGoodsList(page, pageSize) {
      this.loading = true;
      // 构建请求参数
      const params = {
        page: page || this.currentPage,
        limit: pageSize || this.pagesize,
      };

      // 如果有日期范围，添加到请求参数
      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.formatDate(this.dateRange[0]);
        params.endTime = this.formatDate(this.dateRange[1]);
      }

      // 调用API获取商品列表
      getGoodsList(params)
        .then((res) => {
          this.loading = false;
          if (res && res.data) {
            // 适配返回的数据格式
            this.goodsList = res.data.list || [];

            // 处理图片数据，将字符串转为数组
            this.goodsList.forEach((item) => {
              if (typeof item.goodsCover === "string") {
                item.goodsCover = item.goodsCover
                  ? item.goodsCover.split(",")
                  : [];
              }
              if (typeof item.goodsPic === "string") {
                item.goodsPic = item.goodsPic ? item.goodsPic.split(",") : [];
              }
            });

            this.totalnum = res.data.total || 0;
            this.currentPage = res.data.pageNum || 1;
            this.pagesize = res.data.pageSize || 10;
          } else {
            this.$message.error("获取商品列表失败");
            this.goodsList = [];
            this.totalnum = 0;
          }
        })
        .catch((err) => {
          console.error("获取商品列表出错:", err);
          this.$message.error("获取商品列表失败");
          this.goodsList = [];
          this.totalnum = 0;
        });
    },
    handleSizeChange: function (pagesize) {
      this.pagesize = pagesize;
      this.fetchGoodsList(this.currentPage, this.pagesize);
    },
    // 监听页数变化
    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchGoodsList(this.currentPage, this.pagesize);
    },
    // 添加商品
    addGoods() {
      this.dialogTitle = "添加商品";
      this.goodsForm = {
        goodsId: "",
        goodsName: "",
        goodsCover: [],
        goodsPic: [],
        goodsTotalPrice: 0,
        goodsRealPay: 0,
        deductionPoints: 0,
        freight: 0,
        uploadTime: "",
      };
      this.coverFileList = [];
      this.picFileList = [];
      this.dialogVisible = true;
    },
    // 编辑商品
    editGoods(row) {
      this.dialogTitle = "编辑商品";
      this.goodsForm = JSON.parse(JSON.stringify(row));

      // 处理图片数据
      // 如果后端返回的是字符串，则转换为数组
      if (typeof this.goodsForm.goodsCover === "string") {
        this.goodsForm.goodsCover = this.goodsForm.goodsCover
          ? this.goodsForm.goodsCover.split(",")
          : [];
      }
      if (typeof this.goodsForm.goodsPic === "string") {
        this.goodsForm.goodsPic = this.goodsForm.goodsPic
          ? this.goodsForm.goodsPic.split(",")
          : [];
      }

      // 处理图片列表
      this.coverFileList = (this.goodsForm.goodsCover || []).map((url) => ({
        url,
      }));
      this.picFileList = (this.goodsForm.goodsPic || []).map((url) => ({
        url,
      }));

      this.dialogVisible = true;
    },
    // 删除商品
    deleteGoods(row) {
      this.$confirm("确认删除该商品?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 调用updateGoodsInfo接口更新商品状态为已删除
          const params = {
            goodsId: row.goodsId,
            logicalDel: 1, // 设置为已删除状态
          };

          updateGoodsInfo(params)
            .then((res) => {
              if (res && res.data && res.data.code === 200) {
                this.$message.success("删除成功");
                this.fetchGoodsList(this.currentPage, this.pagesize);
              } else {
                this.$message.error(res.data.message || "删除失败");
              }
            })
            .catch((err) => {
              console.error("删除商品出错:", err);
              this.$message.error("删除失败");
            });
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    // 商品封面选择变化
    handleCoverChange(file, fileList) {
      this.coverFileList = fileList;

      if (file && file.raw) {
        // 创建FormData对象上传图片
        const formData = new FormData();
        formData.append("file", file.raw);

        // 上传前临时预览
        file.url = URL.createObjectURL(file.raw);

        // 调用上传图片接口
        uploadImage(formData)
          .then((result) => {
            if (result && result.data && result.data.code === 200) {
              // 更新单个文件的url
              file.url = result.data.data;

              // 构建所有图片的URL数组
              this.goodsForm.goodsCover = this.coverFileList
                .map((item) => item.url || "")
                .filter((url) => url);

              this.$message.success("封面上传成功");
            } else {
              this.$message.error(result.data.message || "封面上传失败");
              // 移除上传失败的文件
              this.coverFileList = this.coverFileList.filter((f) => f !== file);
            }
          })
          .catch((err) => {
            console.error("封面上传出错:", err);
            this.$message.error("封面上传失败");
            // 移除上传失败的文件
            this.coverFileList = this.coverFileList.filter((f) => f !== file);
          });
      }
    },
    // 商品封面删除
    handleCoverRemove(file, fileList) {
      this.coverFileList = fileList;
      // 直接将文件列表的URL更新到goodsForm中
      this.goodsForm.goodsCover = this.coverFileList
        .map((item) => item.url || "")
        .filter((url) => url);
    },
    // 商品详情图片选择变化
    handlePicChange(file, fileList) {
      this.picFileList = fileList;

      if (file && file.raw) {
        // 创建FormData对象上传图片
        const formData = new FormData();
        formData.append("file", file.raw);

        // 上传前临时预览
        file.url = URL.createObjectURL(file.raw);

        // 调用上传图片接口
        uploadImage(formData)
          .then((result) => {
            if (result && result.data && result.data.code === 200) {
              // 更新单个文件的url
              file.url = result.data.data;

              // 构建所有图片的URL数组
              this.goodsForm.goodsPic = this.picFileList
                .map((item) => item.url || "")
                .filter((url) => url);

              this.$message.success("详情图片上传成功");
            } else {
              this.$message.error(result.data.message || "详情图片上传失败");
              // 移除上传失败的文件
              this.picFileList = this.picFileList.filter((f) => f !== file);
            }
          })
          .catch((err) => {
            console.error("详情图片上传出错:", err);
            this.$message.error("详情图片上传失败");
            // 移除上传失败的文件
            this.picFileList = this.picFileList.filter((f) => f !== file);
          });
      }
    },
    // 商品详情图片删除
    handlePicRemove(file, fileList) {
      this.picFileList = fileList;
      // 直接将文件列表的URL更新到goodsForm中
      this.goodsForm.goodsPic = this.picFileList
        .map((item) => item.url || "")
        .filter((url) => url);
    },
    // 提交表单
    submitForm() {
      this.$refs.goodsForm.validate((valid) => {
        if (valid) {
          // 复制表单数据
          const params = {
            ...this.goodsForm,
          };

          // 如果需要转换数组为字符串再提交，取决于后端API需求
          if (Array.isArray(params.goodsCover)) {
            params.goodsCover = params.goodsCover.join(",");
          }

          if (Array.isArray(params.goodsPic)) {
            params.goodsPic = params.goodsPic.join(",");
          }

          // 如果是添加商品，设置默认值
          if (!params.goodsId) {
            params.logicalDel = 0; // 默认未删除状态
            // 使用addGoods接口添加新商品
            addGoods(params)
              .then((res) => {
                if (res && res.data && res.data.code === 200) {
                  this.$message.success("添加成功");
                  this.dialogVisible = false;
                  // 添加操作，回到第一页
                  this.fetchGoodsList(1, this.pagesize);
                } else {
                  this.$message.error(res.data.message || "添加失败");
                }
              })
              .catch((err) => {
                console.error("添加商品出错:", err);
                this.$message.error("添加失败");
              });
          } else {
            // 使用updateGoodsInfo接口更新商品
            console.log(params);
            updateGoodsInfo(params)
              .then((res) => {
                if (res && res.data && res.data.code === 200) {
                  this.$message.success("更新成功");
                  this.dialogVisible = false;
                  this.fetchGoodsList(this.currentPage, this.pagesize);
                } else {
                  this.$message.error(res.data.message || "更新失败");
                }
              })
              .catch((err) => {
                console.error("更新商品出错:", err);
                this.$message.error("更新失败");
              });
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 6px;
  background-color: #f5f7fa;
}

/* 操作区域样式 */
.action-section {
  margin-bottom: 8px;
}

.action-card {
  background: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

.action-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.action-icon {
  font-size: 20px;
  color: #409eff;
  margin-right: 6px;
}

.action-title {
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-btn {
  border-radius: 8px;
  padding: 12px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-btn {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
}

.add-btn:hover {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 8px;
}

.table-card {
  background: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.table-title {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  color: #303133;
}

.table-title i {
  font-size: 20px;
  color: #409eff;
  margin-right: 6px;
}

.modern-table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ebeef5;
}

.modern-table .el-table__header-wrapper {
  border-radius: 8px 8px 0 0;
}

.modern-table .el-table__row {
  transition: all 0.3s ease;
}

.modern-table .el-table__row:hover {
  background-color: #f8faff !important;

  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.goods-id {
  font-family: "Monaco", "Consolas", monospace;
  font-size: 12px;
  color: #606266;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.goods-name {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.goods-icon {
  color: #409eff;
  font-size: 1rem;
}

.price-text {
  font-weight: 600;
  font-size: 0.8rem;
}

.total-price {
  color: #f56c6c;
}

.real-price {
  color: #67c23a;
}

.points-text {
  color: #e6a23c;
  font-weight: 500;
}

.freight-text {
  color: #909399;
  font-size: 12px;
}

.time-text {
  color: #909399;
  font-size: 12px;
}

.parameter-text,
.description-text {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.operation-btn {
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 6px;
}

.edit-btn {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
}

.edit-btn:hover {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
}

.delete-btn {
  background: linear-gradient(135deg, #f56c6c 0%, #f56c6c 100%);
  border: none;
}

.delete-btn:hover {
  background: linear-gradient(135deg, #f56c6c 0%, #f56c6c 100%);
}

/* 分页区域样式 */
.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.modern-pagination {
  background: white;
  border-radius: 8px;
  padding: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

/* 图片预览样式 */
.image-preview {
  position: relative;
  display: inline-block;
}

.image-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 0.8rem;
}

/* 对话框样式 */
.el-dialog {
  border-radius: 8px;
  overflow: hidden;
}

.el-dialog__header {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  color: white;
  padding: 8px 10px;
}

.el-dialog__title {
  color: white;
  font-weight: 600;
}

.el-dialog__headerbtn .el-dialog__close {
  color: white;
}

.el-dialog__body {
  padding: 10px;
}

.el-form-item__label {
  font-weight: 500;
  color: #303133;
}

.el-input__inner,
.el-textarea__inner {
  border-radius: 6px;
}

.el-input-number {
  width: 100%;
}

.el-date-editor {
  width: 100%;
}

.dialog-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.dialog-footer .el-button {
  border-radius: 6px;
  padding: 10px 20px;
  font-weight: 500;
}

/* 上传组件样式 */
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 148px;
  height: 148px;
  line-height: 148px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
