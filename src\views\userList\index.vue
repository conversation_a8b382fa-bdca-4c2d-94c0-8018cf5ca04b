﻿<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-card">
        <div class="search-header">
          <i class="el-icon-search search-icon" />
          <span class="search-title">用户查询</span>
        </div>
        <div class="search-content">
          <div class="search-input-wrapper">
            <el-input
              v-model="phone"
              placeholder="请输入手机号码进行搜索"
              prefix-icon="el-icon-mobile-phone"
              clearable
              @keyup.enter.native="searchItem"
            />
          </div>
          <div class="search-button-wrapper">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="search-btn"
              @click="searchItem"
            >
              查询
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-card">
        <div class="table-header">
          <div class="table-title">
            <i class="el-icon-user-solid" />
            <span>用户列表</span>
          </div>
        </div>

        <el-table
          :data="userList"
          stripe
          style="width: 100%"
          class="modern-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#374151',
            fontWeight: '600',
          }"
          :row-style="{ height: '60px' }"
        >
          <el-table-column prop="id" label="用户ID" width="100" align="center">
            <template slot-scope="scope">
              <span class="user-id">#{{ scope.row.id }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="userName" label="昵称" min-width="150">
            <template slot-scope="scope">
              <div class="user-name-cell">
                <div class="user-avatar">
                  {{ scope.row.userName.charAt(0) }}
                </div>
                <span>{{ scope.row.userName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="phnumber" label="手机号" min-width="140">
            <template slot-scope="scope">
              <span class="phone-number">{{ scope.row.phnumber }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="realName" label="真实姓名" min-width="120">
            <template slot-scope="scope">
              <span class="real-name">{{ scope.row.realName || "-" }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="address"
            label="邮寄地址"
            min-width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span class="address">{{ scope.row.address || "-" }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="carId" label="车牌号" min-width="120">
            <template slot-scope="scope">
              <span class="car-id">{{ scope.row.carId || "-" }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="integral"
            label="积分"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag type="success" size="small" class="integral-tag">
                {{ scope.row.integral }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="relationship" label="归属关系" min-width="120">
            <template slot-scope="scope">
              <span class="relationship">{{
                scope.row.relationship || "-"
              }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" align="center">
            <template slot-scope="scope">
              <el-button
                size="small"
                type="primary"
                icon="el-icon-view"
                class="action-btn"
                @click="handleInfo(scope.row)"
              >
                详细
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <el-pagination
        background
        layout="total, prev, pager, next, jumper"
        :total="filterParam.total"
        :page-size="filterParam.limit"
        :current-page="filterParam.page"
        class="modern-pagination"
        @current-change="changePage"
      />
    </div>

    <!-- 用户详情对话框 -->
    <el-dialog
      title="用户详细信息"
      :visible.sync="dialogVisible"
      width="600px"
      :before-close="handleClose"
      class="user-dialog"
    >
      <div class="dialog-content">
        <div class="user-info-grid">
          <div class="info-item">
            <label>用户ID</label>
            <el-input :disabled="true" :value="showData.id" />
          </div>

          <div class="info-item">
            <label>手机号</label>
            <el-input :disabled="true" :value="showData.phone" />
          </div>

          <div class="info-item">
            <label>用户名</label>
            <el-input :disabled="true" :value="showData.userName" />
          </div>

          <div class="info-item">
            <label>积分</label>
            <el-input v-model="showData.integral" placeholder="请输入积分" />
          </div>

          <div class="info-item">
            <label>邀请码</label>
            <el-input
              v-model="showData.invitationCode"
              placeholder="请输入邀请码"
            />
          </div>

          <div class="info-item">
            <label>注册时间</label>
            <el-input :disabled="true" :value="showData.createTime" />
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="dialogVisible = false"
          >取消</el-button
        >
        <el-button type="primary" class="confirm-btn" @click="updT"
          >保存修改</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAllUserInfo, updT, getUserInfoByPhone } from "@/api/userList";
export default {
  data() {
    return {
      filterParam: {
        page: 1,
        limit: 10,
        total: 0,
        phone: "",
      },
      userList: [],
      phone: "",
      dialogVisible: false,
      showData: {},
    };
  },
  mounted() {
    this.getUserList();
  },
  methods: {
    getUserList() {
      const data = {
        page: this.filterParam.page,
        limit: this.filterParam.limit,
        adminRabk: "0",
        phone: this.filterParam.phone === "" ? null : this.filterParam.phone,
      };
      getAllUserInfo(data).then((res) => {
        console.log(res);
        res = res.data;
        this.userList = res.data.dataList;
        this.filterParam.total = res.data.total;
      });
    },
    changePage(e) {
      this.filterParam.page = e;
      this.getUserList();
    },
    searchItem() {
      this.filterParam.phone = this.phone;
      this.filterParam.page = 1;
      this.filterParam.total = 0;
      if (this.filterParam.phone === "") {
        this.getUserList();
      } else {
        this.getUserInfoByPhone();
      }
    },
    handleInfo(user) {
      this.showData = { ...user };
      this.dialogVisible = true;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
    updT() {
      console.log(this.showData);
      this.$confirm("是否修改用户信息？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          updT(this.showData).then((res) => {
            if (res.data === "updSuccess") {
              this.$message({
                message: "修改成功",
                type: "success",
              });
              this.dialogVisible = false;
              this.getUserList();
            } else {
              this.$message.error("修改失败");
            }
          });
        })
        .catch(() => {
          // 取消操作
        });
    },
    getUserInfoByPhone() {
      const phone = this.filterParam.phone;
      let showData = [];
      this.filterParam.total = 0;
      getUserInfoByPhone({
        phone,
      })
        .then((res) => {
          res = res.data;
          if (res.code !== 200) {
            return;
          }
          if (res.data) {
            showData = [res.data];
            this.filterParam.total = 1;
          }
        })
        .finally(() => {
          this.userList = showData;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.app-container {
  padding: 6px;
  background: transparent;
}

// 搜索区域样式
.search-section {
  margin-bottom: 8px;
}

.search-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .search-header {
    display: flex;
    align-items: center;
    margin-bottom: 6px;

    .search-icon {
      font-size: 1.1rem;
      color: #409eff;
      margin-right: 6px;
    }

    .search-title {
      font-size: 1rem;
      font-weight: 600;
      color: #374151;
    }
  }

  .search-content {
    display: flex;
    gap: 8px;
    align-items: center;

    .search-input-wrapper {
      flex: 1;
      max-width: 300px;
    }

    .search-button-wrapper {
      .search-btn {
        background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
        border: none;
        padding: 6px 12px;
        border-radius: 6px;
        transition: all 0.3s ease;
        font-size: 0.9rem;
        &:hover {
          box-shadow: 0 3px 12px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }
}

// 表格区域样式
.table-section {
  margin-bottom: 8px;
}

.table-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .table-header {
    margin-bottom: 8px;

    .table-title {
      display: flex;
      align-items: center;
      font-size: 1.1rem;
      font-weight: 600;
      color: #374151;

      i {
        margin-right: 6px;
        color: #409eff;
      }
    }
  }
}

.modern-table {
  border-radius: 8px;
  overflow: hidden;

  :deep(.el-table__row) {
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.01);
      box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .user-id {
    font-weight: 600;
    color: #409eff;
  }

  .user-name-cell {
    display: flex;
    align-items: center;
    gap: 10px;

    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 0.8rem;
    }
  }

  .phone-number {
    font-family: "Monaco", "Menlo", monospace;
    color: #059669;
    font-weight: 500;
  }

  .integral-tag {
    font-weight: 600;
  }

  .action-btn {
    background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
    border: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    &:hover {
      box-shadow: 0 3px 12px rgba(64, 158, 255, 0.4);
    }
  }
}

// 分页样式
.pagination-section {
  display: flex;
  justify-content: right;
  margin-top: 8px;
}

.modern-pagination {
  :deep(.el-pager li) {
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.3s ease;

    &.active {
      background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
      border-color: transparent;
    }

    &:hover {
      background: rgba(64, 158, 255, 0.1);
    }
  }

  :deep(.btn-prev),
  :deep(.btn-next) {
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(64, 158, 255, 0.1);
    }
  }
}

// 对话框样式
:deep(.user-dialog) {
  .el-dialog {
    border-radius: 8px;
    overflow: hidden;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
    color: white;
    padding: 8px 10px;

    .el-dialog__title {
      color: white;
      font-weight: 600;
    }

    .el-dialog__close {
      color: white;
      font-size: 1.1rem;

      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  .el-dialog__body {
    padding: 10px;
  }

  .el-dialog__footer {
    padding: 16px 24px;
    background: #f8fafc;
  }
}

.dialog-content {
  .user-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;

    .info-item {
      label {
        display: block;
        margin-bottom: 4px;
        font-weight: 600;
        color: #374151;
        font-size: 0.8rem;
      }

      .el-input {
        :deep(.el-input__inner) {
          border-radius: 8px;
          border: 1px solid #e5e7eb;
          transition: all 0.3s ease;

          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
          }
        }
      }
    }
  }
}

.dialog-footer {
  .cancel-btn {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    color: #6b7280;

    &:hover {
      background: #f3f4f6;
      border-color: #9ca3af;
    }
  }

  .confirm-btn {
    background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
    border: none;
    border-radius: 8px;
    margin-left: 12px;
    &:hover {
      box-shadow: 0 3px 12px rgba(64, 158, 255, 0.4);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 4px;
  }

  .search-content {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;

    .search-input-wrapper {
      max-width: none;
    }
  }

  .dialog-content .user-info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}
</style>
