import Cookies from 'js-cookie'

const Token<PERSON>ey = 'token'
const userIdKey = 'userid'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(Token<PERSON>ey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function setUserId(userid) {
  return Cookies.set(userIdKey, userid)
}

export function getUserId() {
  return Cookies.get(userIdKey)
}

export function removeUserId() {
  return Cookies.remove(userIdKey)
}
