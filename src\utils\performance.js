/**
 * 性能监控工具
 * 用于监控应用启动和运行时性能
 */

class PerformanceMonitor {
  constructor() {
    this.marks = new Map()
    this.measures = new Map()
    this.isEnabled = process.env.NODE_ENV === 'development'
  }

  // 标记时间点
  mark(name) {
    if (!this.isEnabled) return

    const timestamp = performance.now()
    this.marks.set(name, timestamp)

    if (typeof performance.mark === 'function') {
      performance.mark(name)
    }

    console.log(`🏁 Performance Mark: ${name} at ${timestamp.toFixed(2)}ms`)
  }

  // 测量两个时间点之间的耗时
  measure(name, startMark, endMark) {
    if (!this.isEnabled) return

    const startTime = this.marks.get(startMark)
    const endTime = this.marks.get(endMark)

    if (startTime && endTime) {
      const duration = endTime - startTime
      this.measures.set(name, duration)

      if (typeof performance.measure === 'function') {
        performance.measure(name, startMark, endMark)
      }

      console.log(
        `⏱️  Performance Measure: ${name} took ${duration.toFixed(2)}ms`
      )
      return duration
    }
  }

  // 获取所有测量结果
  getAllMeasures() {
    return Object.fromEntries(this.measures)
  }

  // 清除所有标记和测量
  clear() {
    this.marks.clear()
    this.measures.clear()

    if (typeof performance.clearMarks === 'function') {
      performance.clearMarks()
    }
    if (typeof performance.clearMeasures === 'function') {
      performance.clearMeasures()
    }
  }

  // 监控路由切换性能
  monitorRouteChange(to, from) {
    if (!this.isEnabled) return

    const routeName = to.name || to.path
    this.mark(`route-start-${routeName}`)

    // 在下一个 tick 中标记路由结束
    this.$nextTick(() => {
      this.mark(`route-end-${routeName}`)
      this.measure(
        `route-${routeName}`,
        `route-start-${routeName}`,
        `route-end-${routeName}`
      )
    })
  }

  // 监控组件渲染性能
  monitorComponent(componentName) {
    if (!this.isEnabled) return

    return {
      beforeCreate: () => {
        this.mark(`${componentName}-create-start`)
      },
      created: () => {
        this.mark(`${componentName}-create-end`)
        this.measure(
          `${componentName}-create`,
          `${componentName}-create-start`,
          `${componentName}-create-end`
        )
      },
      beforeMount: () => {
        this.mark(`${componentName}-mount-start`)
      },
      mounted: () => {
        this.mark(`${componentName}-mount-end`)
        this.measure(
          `${componentName}-mount`,
          `${componentName}-mount-start`,
          `${componentName}-mount-end`
        )
      }
    }
  }

  // 监控 API 请求性能
  monitorAPI(url, method = 'GET') {
    if (!this.isEnabled) return

    const requestId = `${method}-${url}-${Date.now()}`
    this.mark(`api-start-${requestId}`)

    return {
      end: () => {
        this.mark(`api-end-${requestId}`)
        this.measure(
          `api-${requestId}`,
          `api-start-${requestId}`,
          `api-end-${requestId}`
        )
      }
    }
  }

  // 生成性能报告
  generateReport() {
    if (!this.isEnabled) return

    const measures = this.getAllMeasures()
    const report = {
      timestamp: new Date().toISOString(),
      measures,
      summary: {
        totalMeasures: Object.keys(measures).length,
        slowestOperation: null,
        fastestOperation: null,
        averageTime: 0
      }
    }

    if (Object.keys(measures).length > 0) {
      const times = Object.values(measures)
      const maxTime = Math.max(...times)
      const minTime = Math.min(...times)
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length

      report.summary.slowestOperation = Object.entries(measures).find(
        ([, time]) => time === maxTime
      )
      report.summary.fastestOperation = Object.entries(measures).find(
        ([, time]) => time === minTime
      )
      report.summary.averageTime = avgTime
    }

    console.group('📊 Performance Report')
    console.table(measures)
    console.log('Summary:', report.summary)
    console.groupEnd()

    return report
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

// Vue 插件
export const PerformancePlugin = {
  install(Vue) {
    Vue.prototype.$performance = performanceMonitor

    // 全局混入，监控所有组件
    Vue.mixin({
      beforeCreate() {
        if (this.$options.name) {
          performanceMonitor.mark(`${this.$options.name}-create-start`)
        }
      },
      created() {
        if (this.$options.name) {
          performanceMonitor.mark(`${this.$options.name}-create-end`)
          performanceMonitor.measure(
            `${this.$options.name}-create`,
            `${this.$options.name}-create-start`,
            `${this.$options.name}-create-end`
          )
        }
      }
    })
  }
}

export default performanceMonitor
