<template>
  <nav class="breadcrumb-nav" role="navigation" aria-label="面包屑导航">
    <div class="breadcrumb-container">
      <!-- 首页链接已在Context7中处理，移除重复显示 -->

      <!-- 动态面包屑项 -->
      <template v-for="(item, index) in breadcrumbList">
        <!-- 分隔符 - 只在非第一项前显示 -->
        <div
          v-if="index > 0"
          :key="`separator-${index}`"
          class="breadcrumb-separator"
          aria-hidden="true"
        >
          <svg
            class="separator-icon"
            viewBox="0 0 24 24"
            width="12"
            height="12"
          >
            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z" />
          </svg>
        </div>

        <!-- 面包屑项 -->
        <div :key="item.path || `item-${index}`" class="breadcrumb-item">
          <component
            :is="isLastItem(index) ? 'span' : 'router-link'"
            :to="isLastItem(index) ? null : item.path"
            :class="[
              'breadcrumb-link',
              {
                'current-page': isLastItem(index),
                clickable: !isLastItem(index),
              },
            ]"
            :aria-current="isLastItem(index) ? 'page' : null"
            :title="(item.meta && item.meta.title) || item.name"
          >
            <!-- 图标 -->
            <svg
              v-if="
                item.meta &&
                item.meta.icon &&
                item.meta.icon.indexOf('el-icon') === 0
              "
              class="breadcrumb-icon"
              viewBox="0 0 24 24"
              width="16"
              height="16"
            >
              <use :href="`#${item.meta.icon}`" />
            </svg>
            <svg-icon
              v-else-if="item.meta && item.meta.icon"
              :icon-class="item.meta.icon"
              class="breadcrumb-icon"
            />

            <!-- 文本 -->
            <span class="breadcrumb-text">{{
              (item.meta && item.meta.title) || item.name
            }}</span>
          </component>
        </div>
      </template>
    </div>

    <!-- 移动端下拉菜单 -->
    <div v-if="isMobile" class="breadcrumb-mobile">
      <el-dropdown placement="bottom-start" @command="handleDropdownCommand">
        <button
          class="mobile-breadcrumb-trigger"
          aria-label="打开面包屑导航菜单"
        >
          <svg class="mobile-icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z" />
          </svg>
          <span class="mobile-text">{{ currentPageTitle }}</span>
          <svg
            class="dropdown-arrow"
            viewBox="0 0 24 24"
            width="12"
            height="12"
          >
            <path d="M7 10l5 5 5-5z" />
          </svg>
        </button>
        <el-dropdown-menu slot="dropdown" class="breadcrumb-dropdown">
          <el-dropdown-item
            v-for="(item, index) in allBreadcrumbItems"
            :key="item.path || index"
            :command="isCurrentPage(item) ? null : item.path"
            :disabled="isCurrentPage(item)"
            :class="{ 'is-current': isCurrentPage(item) }"
          >
            <div class="dropdown-item-content">
              <svg-icon
                v-if="item.meta && item.meta.icon"
                :icon-class="item.meta.icon"
                class="dropdown-icon"
              />
              <span class="dropdown-text">{{
                (item.meta && item.meta.title) || item.name
              }}</span>
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </nav>
</template>

<script>
import { mapGetters } from "vuex";
import { getBreadcrumbContext7 } from "@/utils/breadcrumb-context7";

export default {
  name: "Breadcrumb",
  props: {
    // 是否显示首页链接
    showHome: {
      type: Boolean,
      default: true,
    },
    // 自定义根路径
    rootPath: {
      type: String,
      default: "/",
    },
    // 最大显示层级
    maxLevel: {
      type: Number,
      default: 5,
    },
    // 是否启用移动端响应式
    responsive: {
      type: Boolean,
      default: true,
    },
    // Context7配置
    context7Options: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      breadcrumbList: [],
      isMobile: false,
      context7: null,
    };
  },

  computed: {
    ...mapGetters(["sidebar"]),

    // 当前页面标题
    currentPageTitle() {
      const current = this.breadcrumbList[this.breadcrumbList.length - 1];
      if (!current) return "当前页面";
      return (
        current.title ||
        (current.meta && current.meta.title) ||
        current.name ||
        "当前页面"
      );
    },

    // 所有面包屑项（包括首页）
    allBreadcrumbItems() {
      const items = [];
      if (this.showHome && this.context7) {
        items.push(this.context7.createHomeItem());
      }
      return items.concat(this.breadcrumbList);
    },
  },

  watch: {
    $route: {
      handler: "generateBreadcrumb",
      immediate: true,
    },
  },

  created() {
    // 初始化Context7实例
    this.initContext7();
  },

  mounted() {
    this.handleResize();
    if (this.responsive) {
      window.addEventListener("resize", this.handleResize);
    }
  },

  beforeDestroy() {
    if (this.responsive) {
      window.removeEventListener("resize", this.handleResize);
    }

    // 清理Context7资源
    if (this.context7) {
      this.context7.removeListener(this.onBreadcrumbUpdate);
    }
  },

  methods: {
    // 初始化Context7
    initContext7() {
      const options = {
        showHome: this.showHome,
        rootPath: this.rootPath,
        maxLevel: this.maxLevel,
        ...this.context7Options,
      };

      this.context7 = getBreadcrumbContext7(options);

      // 添加面包屑更新监听器
      this.context7.addListener(this.onBreadcrumbUpdate);
    },

    // Context7面包屑更新回调
    onBreadcrumbUpdate(breadcrumb, route) {
      // 可以在这里添加自定义逻辑
      console.log("面包屑已更新:", breadcrumb);
    },

    // 生成面包屑数据（使用Context7）
    generateBreadcrumb() {
      try {
        if (!this.context7) {
          this.initContext7();
        }

        // 使用Context7生成面包屑
        this.breadcrumbList = this.context7.generateBreadcrumb(this.$route);

        // 发出事件供父组件监听
        this.$emit("breadcrumb-change", this.breadcrumbList, this.$route);
      } catch (error) {
        console.error("生成面包屑失败:", error);
        this.breadcrumbList = [];
      }
    },

    // 编译路径（使用Context7）
    compilePath(path) {
      if (this.context7) {
        return this.context7.compilePath(path, this.$route.params);
      }

      // 回退方案
      try {
        const { params } = this.$route;
        return path.replace(/:(\w+)/g, (match, key) => params[key] || match);
      } catch (error) {
        console.error("编译路径失败:", error);
        return path;
      }
    },

    // 判断是否为最后一项
    isLastItem(index) {
      return index === this.breadcrumbList.length - 1;
    },

    // 判断是否为当前页面
    isCurrentPage(item) {
      return item.path === this.$route.path || item.isCurrent;
    },

    // 处理下拉菜单选择
    handleDropdownCommand(path) {
      if (path && path !== this.$route.path && path !== null) {
        this.$router.push(path);
      }
    },

    // 处理窗口大小变化
    handleResize() {
      this.isMobile = window.innerWidth <= 768;
    },

    // 获取面包屑分析数据
    getBreadcrumbAnalysis() {
      if (this.context7) {
        return this.context7.analyzeBreadcrumb(this.breadcrumbList);
      }
      return null;
    },

    // 更新Context7配置
    updateContext7Config(newOptions) {
      if (this.context7) {
        this.context7.updateConfig(newOptions);
        this.generateBreadcrumb(); // 重新生成面包屑
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.breadcrumb-nav {
  position: relative;
  width: 100%;

  .breadcrumb-container {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    gap: 4px;
    padding: 8px 0;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    // 移动端隐藏
    @media (max-width: 768px) {
      display: none;
    }
  }

  .breadcrumb-item {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    position: relative;
  }

  .breadcrumb-link {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    border-radius: 6px;
    color: #64748b;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    transition: all 0.2s ease;
    white-space: nowrap;
    position: relative;

    &:hover {
      background-color: rgba(59, 130, 246, 0.1);
      color: #3b82f6;
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    &.home-link {
      color: #6b7280;

      &:hover {
        color: #3b82f6;
      }
    }

    &.current-page {
      color: #1f2937;
      font-weight: 600;
      cursor: default;

      &:hover {
        background-color: transparent;
        transform: none;
      }
    }

    &.clickable {
      cursor: pointer;

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        border-radius: 1px;
        transition: all 0.3s ease;
        transform: translateX(-50%);
      }

      &:hover::after {
        width: 80%;
      }
    }
  }

  .breadcrumb-icon {
    fill: currentColor;
    flex-shrink: 0;
  }

  .breadcrumb-text {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .breadcrumb-separator {
    display: flex;
    align-items: center;
    margin: 0 2px;
    opacity: 0.5;

    .separator-icon {
      fill: currentColor;
      color: #9ca3af;
    }
  }

  // 移动端面包屑
  .breadcrumb-mobile {
    display: none;

    @media (max-width: 768px) {
      display: block;
    }
  }

  .mobile-breadcrumb-trigger {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #ffffff;
    color: #374151;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
    max-width: 240px;

    &:hover {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }

  .mobile-icon {
    fill: currentColor;
    flex-shrink: 0;
  }

  .mobile-text {
    flex: 1;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .dropdown-arrow {
    fill: currentColor;
    flex-shrink: 0;
    opacity: 0.7;
  }
}

// 下拉菜单样式
:deep(.breadcrumb-dropdown) {
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 4px 0;
  min-width: 200px;

  .el-dropdown-menu__item {
    padding: 0;

    &:hover {
      background-color: rgba(59, 130, 246, 0.1);
    }

    &.is-current {
      background-color: rgba(59, 130, 246, 0.1);
      color: #3b82f6;
      font-weight: 600;
    }
  }
}

.dropdown-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  width: 100%;
}

.dropdown-icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

.dropdown-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .breadcrumb-nav {
    .breadcrumb-link {
      color: #9ca3af;

      &:hover {
        background-color: rgba(59, 130, 246, 0.2);
        color: #60a5fa;
      }

      &.current-page {
        color: #f9fafb;
      }
    }

    .mobile-breadcrumb-trigger {
      border-color: #374151;
      background: #1f2937;
      color: #f9fafb;

      &:hover {
        border-color: #60a5fa;
        box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
      }
    }
  }

  :deep(.breadcrumb-dropdown) {
    background: #1f2937;
    border-color: #374151;

    .el-dropdown-menu__item {
      color: #f9fafb;

      &:hover {
        background-color: rgba(96, 165, 250, 0.2);
      }
    }
  }
}

// 动画效果
@keyframes breadcrumbSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.breadcrumb-item {
  animation: breadcrumbSlideIn 0.3s ease-out;
}

// 无障碍功能增强
@media (prefers-reduced-motion: reduce) {
  .breadcrumb-link {
    transition: none;
  }

  .breadcrumb-item {
    animation: none;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .breadcrumb-link {
    border: 1px solid transparent;

    &:focus {
      border-color: currentColor;
      outline: 2px solid currentColor;
      outline-offset: 2px;
    }
  }
}
</style>
