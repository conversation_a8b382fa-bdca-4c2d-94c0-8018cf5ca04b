﻿<template>
  <div
    class="h-15 overflow-hidden relative bg-transparent px-5 flex justify-between items-center"
  >
    <div class="flex items-center">
      <div
        class="flex items-center h-full cursor-pointer transition-all duration-300 rounded-lg hover:bg-blue-500/10 hover:scale-105 p-2"
        @click="toggleSideBar"
      >
        <hamburger :is-active="sidebar.opened" />
      </div>
      <breadcrumb class="ml-2" />
    </div>

    <div class="flex items-center h-full">
      <el-dropdown class="h-full" trigger="click">
        <div
          class="h-full relative flex items-center px-3 py-2 rounded-3xl transition-all duration-300 cursor-pointer hover:bg-blue-500/10 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-500/20"
        >
          <img
            src="https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif?imageView2/1/w/80/h/80"
            class="w-9 h-9 rounded-full border-2 border-blue-500/20 hover:border-blue-500/60 transition-all duration-300 cursor-pointer"
          />
          <i
            class="el-icon-caret-bottom cursor-pointer ml-2 text-xs text-gray-400 hover:text-blue-500 transition-all duration-300"
          />
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <el-dropdown-item @click.native="goToProfile">
            <span class="block">个人信息</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span class="block">退出</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import { removeToken } from "@/utils/auth";

export default {
  components: {
    Breadcrumb,
    Hamburger,
  },
  computed: {
    ...mapGetters(["sidebar", "avatar"]),
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    async logout() {
      removeToken();
      this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    },
    goToProfile() {
      this.$router.push("/profile/index");
    },
  },
};
</script>

<style>
/* 优化下拉菜单样式 - Element UI特殊样式 */
.user-dropdown {
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(229, 231, 235, 0.6);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.user-dropdown .el-dropdown-menu__item {
  padding: 12px 20px;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 4px 8px;
}

.user-dropdown .el-dropdown-menu__item:hover {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  transform: translateX(2px);
}
</style>
