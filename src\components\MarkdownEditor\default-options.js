// Toast UI Editor 默认配置
// 文档: https://nhnent.github.io/tui.editor/api/latest/ToastUIEditor.html#ToastUIEditor

export default {
  // 基础配置
  minHeight: "200px",
  height: "300px",

  // 预览样式：'tab' | 'vertical'
  previewStyle: "vertical",

  // 初始编辑模式：'markdown' | 'wysiwyg'
  initialEditType: "markdown",

  // 初始内容
  initialValue: "",

  // 占位符文本
  placeholder: "请输入内容...",

  // 语言设置
  language: "zh-CN",

  // 功能开关
  useCommandShortcut: true,
  useDefaultHTMLSanitizer: true,
  usageStatistics: false,
  hideModeSwitch: false,

  // 工具栏配置
  toolbarItems: [
    ["heading", "bold", "italic", "strike"],
    ["hr", "quote"],
    ["ul", "ol", "task", "indent", "outdent"],
    ["table", "image", "link"],
    ["code", "codeblock"],
    ["scrollSync"],
  ],

  // 快捷键配置
  shortcuts: {
    "ctrl+b": "bold",
    "ctrl+i": "italic",
    "ctrl+u": "strike",
    "ctrl+k": "link",
    "ctrl+`": "code",
    "ctrl+shift+c": "codeblock",
    "ctrl+shift+k": "table",
    "ctrl+shift+i": "image",
  },

  // 自定义渲染器配置
  customHTMLRenderer: {
    // 自定义 HTML 渲染规则
    htmlBlock: {
      table(node) {
        return [
          { type: "openTag", tagName: "div", classNames: ["table-responsive"] },
          {
            type: "openTag",
            tagName: "table",
            classNames: ["table", "table-bordered", "table-hover"],
          },
        ];
      },
    },
  },

  // 自定义转换器配置
  customMarkdownRenderer: {
    // 自定义 Markdown 渲染规则
  },

  // 主题配置
  theme: "default",

  // 自动保存配置
  autosave: {
    enabled: false,
    delay: 10000, // 10秒
  },

  // 图片上传配置
  imageUpload: {
    enabled: true,
    maxSize: 10 * 1024 * 1024, // 10MB
    acceptTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
    // 上传处理函数需要在组件中定义
    // uploadHandler: null
  },

  // 链接配置
  linkAttributes: {
    target: "_blank",
    rel: "noopener noreferrer",
  },

  // 代码块语言列表
  codeBlockLanguages: [
    "javascript",
    "typescript",
    "python",
    "java",
    "php",
    "go",
    "rust",
    "html",
    "css",
    "scss",
    "less",
    "json",
    "xml",
    "yaml",
    "toml",
    "bash",
    "shell",
    "powershell",
    "sql",
    "markdown",
    "text",
  ],

  // 扩展插件配置
  plugins: [
    // 插件将在组件中配置
  ],

  // 事件钩子配置
  hooks: {
    // 在组件中定义具体的钩子函数
  },

  // 可访问性配置
  accessibility: {
    enabled: true,
    // 键盘导航
    keyboardNavigation: true,
    // 屏幕阅读器支持
    screenReader: true,
  },

  // 性能优化配置
  performance: {
    // 延迟渲染
    lazyRender: true,
    // 虚拟滚动
    virtualScroll: false,
    // 节流延迟
    throttleDelay: 100,
  },
};
