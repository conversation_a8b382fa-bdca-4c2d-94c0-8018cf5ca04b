import { get, post } from '@/utils/requestMethod'
// 不再需要 getAccessTokenById 从这里导入和调用

/**
 * 获取微信小程序评论列表 (通过后端代理)
 * @param {object} params - 查询参数
 * @param {string} params.startTime - 查询时间段的开始时间 (时间戳字符串)
 * @param {string} params.endTime - 查询时间段的结束时间 (时间戳字符串)
 * @param {string} params.filterType - 过滤的数据类型 (可以是空字符串)
 * @param {number} params.offset - 查询的偏移数
 * @param {number} params.limit - 查询每页中的数量
 */
const getWeChatComments = async(params) => {
  try {
    // 直接调用后端代理接口，后端会处理access_token
    // params 对象 (startTime, endTime, filterType, offset, limit) 将被 @/utils/requestMethod 中的 get 函数序列化为查询参数
    return get('/wxComment/comments', params)
  } catch (error) {
    console.error('调用 getWeChatComments (via proxy) 接口失败:', error)
    // 将错误继续抛出，以便调用方可以捕获和处理
    throw error
  }
}

/**
 * 获取单条评论的回复列表 (通过后端代理)
 * @param {object} params - 查询参数
 * @param {string} params.commentId - 评论ID
 */
const getCommentReplies = async({ commentId }) => {
  try {
    // 直接调用后端代理接口
    return get('/wxComment/commentReplies', { commentId })
  } catch (error) {
    console.error(`调用 getCommentReplies (via proxy) 接口失败 (commentId: ${commentId}):`, error)
    throw error
  }
}

/**
 * 删除单条评论的回复 (通过后端代理)
 * @param {object} data - 请求体参数
 * @param {string} data.commentId - 评论ID
 * @param {string} data.replyId - 回复ID
 */
const deleteCommentReply = async(data) => {
  try {
    // 调用后端的删除代理接口，使用POST方法并发送JSON请求体
    // 路径要与后端 WeChatProxyController 中定义的路径一致
    return post('/wxComment/deleteCommentReply', data)
  } catch (error) {
    console.error(`调用 deleteCommentReply (via proxy) 接口失败 (commentId: ${data.commentId}, replyId: ${data.replyId}):`, error)
    throw error
  }
}

export { getWeChatComments, getCommentReplies, deleteCommentReply }
