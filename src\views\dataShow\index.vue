﻿<template>
  <div class="p-1.5 bg-transparent">
    <!-- 页面标题 -->
    <div
      class="text-center mb-2 py-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-sm"
    >
      <h1
        class="text-xl font-bold m-0 mb-0.5 bg-gradient-to-r from-blue-500 to-blue-600 bg-clip-text text-transparent"
      >
        数据统计总览
      </h1>
      <p class="text-xs text-gray-500 m-0 font-normal">实时监控业务关键指标</p>
    </div>

    <!-- 用户数据统计 -->
    <div
      class="mb-2 bg-white/90 backdrop-blur-sm rounded-lg p-2.5 shadow-sm border border-white/20 transition-all duration-300 hover:shadow-md"
    >
      <div
        class="flex items-center mb-1.5 text-base font-semibold text-gray-700"
      >
        <i
          class="el-icon-user mr-1.5 text-lg bg-gradient-to-r from-blue-500 to-blue-600 bg-clip-text text-transparent"
        />
        <span>用户增长</span>
      </div>
      <div class="flex gap-1.5 items-stretch lg:flex-row flex-col">
        <div
          class="min-w-[200px] bg-gradient-to-br from-slate-50 to-slate-200 rounded-md p-2 shadow-inner"
        >
          <div class="flex flex-col gap-1.5">
            <div
              class="flex flex-col items-center p-1.5 rounded bg-gradient-to-br from-blue-500 to-blue-600 text-white border-transparent transition-all duration-300 hover:shadow-lg"
            >
              <div class="text-lg font-bold mb-0.5">
                {{ countAllUsersObj.usersTodayNum }}
              </div>
              <div class="text-xs font-medium">今日新增</div>
            </div>
            <div
              class="flex flex-col items-center p-1.5 rounded bg-white/80 border border-gray-200/40 transition-all duration-300 hover:shadow-lg"
            >
              <div class="text-lg font-bold mb-0.5 text-gray-800">
                {{ countAllUsersObj.allUsersNum }}
              </div>
              <div class="text-xs text-gray-500 font-medium">用户总数</div>
            </div>
          </div>
        </div>
        <div
          class="flex-1 bg-white/60 rounded-md p-1.5 shadow-inner border border-gray-200/30 min-h-[200px] flex flex-col"
        >
          <LineChart
            :echart-data="allUsersByDay.echartData"
            :label="allUsersByDay.label"
          />
          <div
            class="text-center mt-1 text-xs font-semibold text-gray-600 py-0.5 px-2 bg-blue-50 rounded-lg inline-block mx-auto"
          >
            用户增长趋势
          </div>
        </div>
      </div>
    </div>

    <!-- 收入数据统计 -->
    <div
      class="mb-2 bg-white/90 backdrop-blur-sm rounded-lg p-2.5 shadow-sm border border-white/20 transition-all duration-300 hover:shadow-md"
    >
      <div
        class="flex items-center mb-1.5 text-base font-semibold text-gray-700"
      >
        <i
          class="el-icon-money mr-1.5 text-lg bg-gradient-to-r from-blue-500 to-blue-600 bg-clip-text text-transparent"
        />
        <span>收入统计</span>
      </div>
      <div class="flex gap-1.5 items-stretch lg:flex-row flex-col">
        <div
          class="min-w-[200px] bg-gradient-to-br from-slate-50 to-slate-200 rounded-md p-2 shadow-inner"
        >
          <div class="flex flex-col gap-1.5">
            <div
              class="flex flex-col items-center p-1.5 rounded bg-gradient-to-br from-blue-500 to-blue-600 text-white border-transparent transition-all duration-300 hover:shadow-lg"
            >
              <div class="text-lg font-bold mb-0.5">
                ¥{{ allIncomeObj.IncomeToday }}
              </div>
              <div class="text-xs font-medium">今日收入</div>
            </div>
            <div
              class="flex flex-col items-center p-1.5 rounded bg-gradient-to-br from-amber-500 to-orange-600 text-white border-transparent transition-all duration-300 hover:shadow-lg"
            >
              <div class="text-lg font-bold mb-0.5">
                ¥{{ allIncomeObj.RefundToday }}
              </div>
              <div class="text-xs font-medium">今日退款</div>
            </div>
            <div
              class="flex flex-col items-center p-1.5 rounded bg-gradient-to-br from-emerald-500 to-emerald-600 text-white border-transparent transition-all duration-300 hover:shadow-lg"
            >
              <div class="text-lg font-bold mb-0.5">
                ¥{{ allIncomeObj.allIncome }}
              </div>
              <div class="text-xs font-medium">总营业额</div>
            </div>
          </div>
        </div>
        <div
          class="flex-1 bg-white/60 rounded-md p-1.5 shadow-inner border border-gray-200/30 min-h-[200px] flex flex-col"
        >
          <BarChart :value="allMoneyByDay.value" :label="allMoneyByDay.label" />
          <div
            class="text-center mt-1 text-xs font-semibold text-gray-600 py-0.5 px-2 bg-blue-50 rounded-lg inline-block mx-auto"
          >
            收入趋势分析
          </div>
        </div>
      </div>
    </div>

    <!-- 代理数据统计 -->
    <div
      class="mb-2 bg-white/90 backdrop-blur-sm rounded-lg p-2.5 shadow-sm border border-white/20 transition-all duration-300 hover:shadow-md"
    >
      <div
        class="flex items-center mb-1.5 text-base font-semibold text-gray-700"
      >
        <i
          class="el-icon-s-custom mr-1.5 text-lg bg-gradient-to-r from-blue-500 to-blue-600 bg-clip-text text-transparent"
        />
        <span>代理增长</span>
      </div>
      <div class="flex gap-1.5 items-stretch lg:flex-row flex-col">
        <div
          class="min-w-[200px] bg-gradient-to-br from-slate-50 to-slate-200 rounded-md p-2 shadow-inner"
        >
          <div class="flex flex-col gap-1.5">
            <div
              class="flex flex-col items-center p-1.5 rounded bg-gradient-to-br from-blue-500 to-blue-600 text-white border-transparent transition-all duration-300 hover:shadow-lg"
            >
              <div class="text-lg font-bold mb-0.5">
                {{ countAllAgentObj.agentNumToday }}
              </div>
              <div class="text-xs font-medium">今日新增</div>
            </div>
            <div
              class="flex flex-col items-center p-1.5 rounded bg-white/80 border border-gray-200/40 transition-all duration-300 hover:shadow-lg"
            >
              <div class="text-lg font-bold mb-0.5 text-gray-800">
                {{ countAllAgentObj.allAgentNum }}
              </div>
              <div class="text-xs text-gray-500 font-medium">代理总数</div>
            </div>
          </div>
        </div>
        <div
          class="flex-1 bg-white/60 rounded-md p-1.5 shadow-inner border border-gray-200/30 min-h-[200px] flex flex-col"
        >
          <LineChart
            :echart-data="allDlByDay.echartData"
            :label="allDlByDay.label"
          />
          <div
            class="text-center mt-1 text-xs font-semibold text-gray-600 py-0.5 px-2 bg-blue-50 rounded-lg inline-block mx-auto"
          >
            代理增长趋势
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LineChart from "@/components/Charts/LineChart.vue";
import BarChart from "@/components/Charts/BarChart.vue";
import {
  getAllUsersByDay,
  getAllMoneyByDay,
  getAllDlByDay,
  countAllUsers,
  allIncome,
  countAllAgent,
  getAllRefundMoneyByDay,
} from "@/api/dataShow";
export default {
  components: {
    LineChart,
    BarChart,
  },
  data() {
    return {
      allUsersByDay: {
        echartData: [],
        label: [],
      },
      allMoneyByDay: {
        value: [],
        label: [],
      },
      allRefundMoneyByDay: {
        value: [],
        label: [],
      },
      allDlByDay: {
        echartData: [],
        label: [],
      },
      countAllUsersObj: {
        allUsersNum: 0,
        usersTodayNum: 0,
      },
      allIncomeObj: {
        allIncome: 0,
        IncomeToday: 0,
        RefundToday: 0,
      },
      countAllAgentObj: {
        agentNumToday: 0,
        allAgentNum: 0,
      },
    };
  },
  mounted() {
    this.getAllDlByDay();
    this.getAllMoneyByDay();
    this.getAllUsersByDay();
    this.countAllUsers();
    this.allIncome();
    this.countAllAgent();
  },
  methods: {
    getAllUsersByDay() {
      getAllUsersByDay()
        .then((res) => {
          if (res.data.code !== 200) return;
          const data = res.data.data;
          data.map((obj) => {
            this.allUsersByDay.echartData = [
              obj.orderNum,
              ...this.allUsersByDay.echartData,
            ];
            this.allUsersByDay.label = [
              obj.everyday,
              ...this.allUsersByDay.label,
            ];
          });
          this.allUsersByDay.echartData = [
            "",
            ...this.allUsersByDay.echartData,
            "",
          ];
          this.allUsersByDay.label = ["", ...this.allUsersByDay.label, ""];
        })
        .catch((error) => {
          console.error("获取用户数据失败：", error);
        });
    },
    getAllMoneyByDay() {
      getAllMoneyByDay()
        .then((res) => {
          if (res.data.code !== 200) return;
          const data = res.data.data;
          data.forEach((obj) => {
            this.allMoneyByDay.value = [
              obj.orderAllMoney,
              ...this.allMoneyByDay.value,
            ];
            this.allMoneyByDay.label = [
              obj.everyday,
              ...this.allMoneyByDay.label,
            ];
          });
        })
        .catch((error) => {
          console.error("获取收入数据失败：", error);
        });
    },
    getAllRefundMoneyByDay() {
      getAllRefundMoneyByDay()
        .then((res) => {
          if (res.data.code !== 200) return;
          const data = res.data.data;
          data.forEach((obj) => {
            this.allRefundMoneyByDay.value = [
              obj.orderAllMoney,
              ...this.allRefundMoneyByDay.value,
            ];
            this.allRefundMoneyByDay.label = [
              obj.everyday,
              ...this.allRefundMoneyByDay.label,
            ];
          });
        })
        .catch((error) => {
          console.error("获取退款数据失败：", error);
        });
    },

    getAllDlByDay() {
      getAllDlByDay()
        .then((res) => {
          if (res.data.code !== 200) return;
          const data = res.data.data;
          data.map((obj) => {
            this.allDlByDay.echartData = [
              obj.orderNum,
              ...this.allDlByDay.echartData,
            ];
            this.allDlByDay.label = [obj.everyday, ...this.allDlByDay.label];
          });
          this.allDlByDay.echartData = ["", ...this.allDlByDay.echartData, ""];
          this.allDlByDay.label = ["", ...this.allDlByDay.label, ""];
        })
        .catch((error) => {
          console.error("获取代理数据失败：", error);
        });
    },
    allIncome() {
      allIncome()
        .then((res) => {
          res = res.data;
          if (res.code !== 200) {
            console.error("获取收入统计失败");
            return;
          }
          this.allIncomeObj = res.data;
        })
        .catch((error) => {
          console.error("获取收入统计失败：", error);
        });
    },
    countAllUsers() {
      countAllUsers()
        .then((res) => {
          res = res.data;
          if (res.code !== 200) {
            console.error("获取用户统计失败");
            return;
          }
          this.countAllUsersObj = res.data;
        })
        .catch((error) => {
          console.error("获取用户统计失败：", error);
        });
    },
    countAllAgent() {
      countAllAgent()
        .then((res) => {
          res = res.data;
          if (res.code !== 200) {
            console.error("获取代理统计失败");
            return;
          }
          this.countAllAgentObj = res.data;
        })
        .catch((error) => {
          console.error("获取代理统计失败：", error);
        });
    },
  },
};
</script>

<style scoped>
/* TailwindCSS样式沙箱，所有样式已经通过class内联化，这里只保留必要的组件特定样式 */

/* 移动端响应式优化 */
@media (max-width: 640px) {
  .stats-card-mobile {
    min-width: auto;
  }
}

/* 确保页面在小屏幕设备上的可用性 */
@media (max-width: 480px) {
  .stats-number-mobile {
    font-size: 1rem;
  }
  .stats-label-mobile {
    font-size: 0.6rem;
  }
}
</style>
