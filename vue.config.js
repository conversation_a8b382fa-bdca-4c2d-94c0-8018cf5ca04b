"use strict";
const path = require("path");
const defaultSettings = require("./src/settings.js");

function resolve(dir) {
  return path.join(__dirname, dir);
}

const name = defaultSettings.title || "vue Admin Template";
const port = process.env.port || process.env.npm_config_port || 9528;

module.exports = {
  // Tailwind CSS 配置 - 必须保留
  css: {
    loaderOptions: {
      postcss: {
        plugins: [require("tailwindcss"), require("autoprefixer")],
      },
    },
  },

  // 基础配置
  publicPath: "/",
  outputDir: "dist",
  assetsDir: "static",
  lintOnSave: process.env.NODE_ENV !== "development",
  productionSourceMap: false,

  // 开发服务器配置
  devServer: {
    port: port,
    open: true,
    overlay: {
      warnings: false,
      errors: true,
    },
    // API代理配置
    proxy: {
      "/api": {
        target: process.env.VUE_APP_PROXY_TARGET || "http://localhost:8080",
        changeOrigin: true,
        pathRewrite: {
          "^/api": "", // 将 /api 前缀移除，直接转发到目标服务器
        },
        secure: false, // 支持https目标服务器
        logLevel: "info", // 代理日志级别
      },
    },
  },

  configureWebpack: (config) => {
    config.name = name;
    config.resolve.alias = {
      "@": resolve("src"),
    };

    // 生产环境移除 console
    if (process.env.NODE_ENV === "production") {
      config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true;
    }
  },

  chainWebpack(config) {
    // SVG 图标配置 - 项目中有使用，必须保留
    config.module.rule("svg").exclude.add(resolve("src/icons")).end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end();

    // 生产环境代码分割优化
    config.when(process.env.NODE_ENV !== "development", (config) => {
      config
        .plugin("ScriptExtHtmlWebpackPlugin")
        .after("html")
        .use("script-ext-html-webpack-plugin", [
          {
            inline: /runtime\..*\.js$/,
          },
        ])
        .end();

      config.optimization.splitChunks({
        chunks: "all",
        cacheGroups: {
          libs: {
            name: "chunk-libs",
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: "initial",
          },
          elementUI: {
            name: "chunk-elementUI",
            priority: 20,
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/,
          },
          commons: {
            name: "chunk-commons",
            test: resolve("src/components"),
            minChunks: 3,
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      });

      config.optimization.runtimeChunk("single");
    });
  },
};
