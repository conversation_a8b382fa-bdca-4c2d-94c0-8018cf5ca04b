﻿<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-card">
        <div class="header-content">
          <i class="el-icon-chat-line-round header-icon" />
          <span class="header-title">评论管理</span>
          <div class="comment-stats">
            <i class="el-icon-document" />
            <span>共 {{ total }} 条评论</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <div class="search-card">
        <div class="search-header">
          <i class="el-icon-search search-icon" />
          <span class="search-title">评论筛选</span>
          <el-button
            type="text"
            icon="el-icon-refresh"
            class="reset-btn"
            @click="resetFilters"
          >
            重置
          </el-button>
        </div>

        <div class="search-content">
          <div class="search-row">
            <div class="search-item">
              <label>时间范围</label>
              <el-date-picker
                v-model="dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="yyyy-MM-dd HH:mm"
                value-format="timestamp"
                class="date-picker"
                @change="handleDateChange"
              />
            </div>

            <div class="search-item">
              <label>评论状态</label>
              <el-select
                v-model="filterType"
                placeholder="选择状态"
                clearable
                class="status-select"
                @change="fetchComments"
              >
                <el-option label="全部评论" value="" />
                <el-option label="正常评论" value="normal" />
                <el-option label="有回复" value="replied" />
                <el-option label="未回复" value="unreplied" />
              </el-select>
            </div>

            <div class="search-item">
              <label>用户昵称</label>
              <el-input
                v-model="searchKeyword"
                placeholder="搜索用户昵称"
                prefix-icon="el-icon-search"
                clearable
                class="search-input"
                @keyup.enter.native="searchComments"
                @clear="fetchComments"
              />
            </div>
          </div>

          <div class="search-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="search-btn"
              @click="searchComments"
            >
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="fetchComments">
              刷新
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 评论列表区域 -->
    <div class="comments-section">
      <div class="comments-card">
        <div class="card-header">
          <div class="card-title">
            <i class="el-icon-chat-line-round" />
            <span>评论列表</span>
          </div>
          <div class="card-actions">
            <el-dropdown @command="handleBatchAction">
              <el-button type="text" class="batch-btn">
                批量操作<i class="el-icon-arrow-down el-icon--right" />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="delete"
                  icon="el-icon-delete"
                >批量删除</el-dropdown-item>
                <el-dropdown-item
                  command="export"
                  icon="el-icon-download"
                >导出评论</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>

        <div
          v-loading="loading"
          class="comments-content"
          element-loading-text="加载中..."
        >
          <!-- 空状态 -->
          <div v-if="comments.length === 0 && !loading" class="empty-state">
            <i class="el-icon-chat-line-round" />
            <p>暂无评论数据</p>
            <el-button
              type="primary"
              @click="fetchComments"
            >重新加载</el-button>
          </div>

          <!-- 评论列表 -->
          <div v-else class="comment-list">
            <div
              v-for="(comment, commentIndex) in comments"
              :key="comment.id"
              class="comment-item"
            >
              <div class="comment-header">
                <div class="comment-user">
                  <el-checkbox
                    v-model="comment.selected"
                    class="comment-checkbox"
                    @change="handleSelectComment"
                  />
                  <div class="user-avatar">
                    <img :src="comment.avatar" :alt="comment.name">
                  </div>
                  <div class="user-info">
                    <div class="user-name">{{ comment.name }}</div>
                    <div class="comment-meta">
                      <span class="comment-time">
                        <i class="el-icon-time" />
                        {{ comment.time }}
                      </span>
                      <span class="comment-device">
                        <i class="el-icon-mobile-phone" />
                        {{ comment.device }}
                      </span>
                    </div>
                  </div>
                </div>

                <div class="comment-actions">
                  <el-button
                    type="text"
                    icon="el-icon-chat-dot-round"
                    :loading="comment.loadingReplies"
                    class="reply-btn"
                    @click="toggleReplies(commentIndex)"
                  >
                    <span v-if="!comment.repliesLoaded">查看回复</span>
                    <span
                      v-else-if="comment.replies && comment.replies.length > 0"
                    >
                      {{ comment.showReplies ? "隐藏" : "查看" }}
                      {{ comment.replies.length }} 条回复
                    </span>
                    <span v-else>暂无回复</span>
                  </el-button>
                  <el-button
                    type="text"
                    icon="el-icon-delete"
                    class="delete-btn"
                    @click="handleDeleteComment(comment)"
                  >
                    删除
                  </el-button>
                </div>
              </div>

              <div class="comment-content">
                <p>{{ comment.content }}</p>
              </div>

              <!-- 回复区域 -->
              <div
                v-if="comment.showReplies && comment.repliesLoaded"
                class="replies-section"
              >
                <div class="replies-header">
                  <i class="el-icon-chat-dot-round" />
                  <span>回复 ({{
                    comment.replies ? comment.replies.length : 0
                  }})</span>
                </div>

                <div
                  v-if="!comment.replies || comment.replies.length === 0"
                  class="replies-empty"
                >
                  <i class="el-icon-chat-dot-round" />
                  <span>暂无回复</span>
                </div>

                <div v-else class="replies-list">
                  <div
                    v-for="reply in comment.replies"
                    :key="reply.id"
                    class="reply-item"
                  >
                    <div class="reply-avatar">
                      <img :src="reply.avatar" :alt="reply.name">
                    </div>
                    <div class="reply-content">
                      <div class="reply-header">
                        <span class="reply-name">{{ reply.name }}</span>
                        <span class="reply-time">{{ reply.time }}</span>
                      </div>
                      <p class="reply-text">{{ reply.content }}</p>
                    </div>
                    <el-button
                      type="text"
                      icon="el-icon-delete"
                      class="reply-delete-btn"
                      size="mini"
                      @click="handleDeleteReply(commentIndex, reply)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </div>

              <!-- 加载回复状态 -->
              <div v-if="comment.loadingReplies" class="loading-replies">
                <i class="el-icon-loading" />
                <span>正在加载回复...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <el-pagination
        background
        layout="total, prev, pager, next, jumper, sizes"
        :total="total"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :current-page="currentPage"
        class="modern-pagination"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="确认删除"
      :visible.sync="deleteDialogVisible"
      width="400px"
      class="delete-dialog"
    >
      <div class="delete-content">
        <i class="el-icon-warning delete-icon" />
        <div class="delete-message">
          <p>{{ deleteMessage }}</p>
          <p class="delete-warning">此操作不可恢复，请谨慎操作。</p>
        </div>
      </div>
      <span slot="footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" :loading="deleting" @click="confirmDelete">
          确认删除
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getWeChatComments,
  getCommentReplies,
  deleteCommentReply
} from '@/api/commentApi'

export default {
  data() {
    return {
      total: 0,
      currentPage: 1,
      pageSize: 10,
      comments: [],
      loading: false,
      deleting: false,

      // 筛选条件
      dateRange: null,
      startTime: '0',
      endTime: Math.floor(Date.now() / 1000).toString(),
      filterType: '',
      searchKeyword: '',

      // 删除相关
      deleteDialogVisible: false,
      deleteTarget: null,
      deleteMessage: '',

      // 批量操作
      selectedComments: []
    }
  },

  computed: {
    hasSelectedComments() {
      return this.comments.some((comment) => comment.selected)
    }
  },

  created() {
    this.fetchComments()
  },

  methods: {
    async fetchComments() {
      this.loading = true
      try {
        const params = {
          startTime: this.startTime,
          endTime: this.endTime,
          filterType: this.filterType,
          offset: (this.currentPage - 1) * this.pageSize,
          limit: this.pageSize
        }

        if (this.searchKeyword) {
          params.keyword = this.searchKeyword
        }

        const response = await getWeChatComments(params)

        if (response && response.data && response.data.commentList) {
          this.comments = response.data.commentList.map((item) => ({
            id: item.commentId,
            name: item.userInfo?.nickName || '匿名用户',
            avatar: item.userInfo?.headImg || this.getDefaultAvatar(),
            time: this.formatTimestamp(item.createTime) || '未知时间',
            content: item.content?.txt || '无评论内容',
            device: '微信小程序',
            replies: [],
            showReplies: false,
            loadingReplies: false,
            repliesLoaded: false,
            selected: false
          }))
          this.total = response.data.total || 0
        } else {
          this.comments = []
          this.total = 0
        }
      } catch (error) {
        console.error('获取评论失败:', error)
        this.$message.error('获取评论列表失败，请稍后再试')
        this.comments = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    async toggleReplies(commentIndex) {
      const comment = this.comments[commentIndex]
      comment.showReplies = !comment.showReplies

      if (comment.showReplies && !comment.repliesLoaded) {
        comment.loadingReplies = true
        try {
          const response = await getCommentReplies({ commentId: comment.id })
          const fetchedReplies = []

          if (response && response.data && response.data.list) {
            const { reply, commentReplyList } = response.data.list

            if (reply && reply.commentId) {
              fetchedReplies.push({
                id: reply.replyId || `${reply.commentId}-main`,
                name: reply.replyObject?.nickname || '小程序官方',
                avatar: reply.replyObject?.imgUrl || this.getDefaultAvatar(),
                time: this.formatTimestamp(reply.createTime) || '未知时间',
                content: reply.replyContent?.content || ''
              })
            }

            if (commentReplyList && commentReplyList.length > 0) {
              commentReplyList.forEach((cr) => {
                if (cr.commentId && cr.commentReplyId) {
                  fetchedReplies.push({
                    id:
                      cr.commentReplyId ||
                      `${cr.commentId}-${fetchedReplies.length}`,
                    name: cr.commentReplyObject?.nickname || '用户回复',
                    avatar:
                      cr.commentReplyObject?.imgUrl || this.getDefaultAvatar(),
                    time: this.formatTimestamp(cr.createTime) || '未知时间',
                    content: cr.commentReplyContent?.content || ''
                  })
                }
              })
            }
          }

          comment.replies = fetchedReplies
          comment.repliesLoaded = true
        } catch (error) {
          console.error(`获取评论ID ${comment.id} 的回复失败:`, error)
          this.$message.error('获取回复失败，请稍后再试')
          comment.replies = []
        } finally {
          comment.loadingReplies = false
        }
      }
    },

    handleDeleteComment(comment) {
      this.deleteTarget = { type: 'comment', data: comment }
      this.deleteMessage = `确定要删除用户"${comment.name}"的这条评论吗？`
      this.deleteDialogVisible = true
    },

    handleDeleteReply(commentIndex, reply) {
      this.deleteTarget = {
        type: 'reply',
        data: reply,
        commentIndex: commentIndex,
        comment: this.comments[commentIndex]
      }
      this.deleteMessage = `确定要删除用户"${reply.name}"的这条回复吗？`
      this.deleteDialogVisible = true
    },

    async confirmDelete() {
      if (!this.deleteTarget) return

      this.deleting = true
      try {
        if (this.deleteTarget.type === 'reply') {
          const { data: reply, comment } = this.deleteTarget
          const response = await deleteCommentReply({
            commentId: comment.id,
            replyId: reply.id
          })

          if (response && response.data && response.data.errcode === 0) {
            comment.replies = comment.replies.filter((r) => r.id !== reply.id)
            this.$message.success('回复删除成功')
          } else {
            this.$message.error(response?.data?.errmsg || '删除回复失败')
          }
        } else if (this.deleteTarget.type === 'comment') {
          // 这里需要实现删除评论的API调用
          // 暂时只是前端移除，实际项目中需要调用删除评论的API
          const commentId = this.deleteTarget.data.id
          this.comments = this.comments.filter((c) => c.id !== commentId)
          this.total = Math.max(0, this.total - 1)
          this.$message.success('评论删除成功')
        }

        this.deleteDialogVisible = false
      } catch (error) {
        console.error('删除失败:', error)
        this.$message.error('删除操作失败，请检查网络或联系管理员')
      } finally {
        this.deleting = false
        this.deleteTarget = null
      }
    },

    handleDateChange(dates) {
      if (dates && dates.length === 2) {
        this.startTime = Math.floor(dates[0] / 1000).toString()
        this.endTime = Math.floor(dates[1] / 1000).toString()
      } else {
        this.startTime = '0'
        this.endTime = Math.floor(Date.now() / 1000).toString()
      }
      this.fetchComments()
    },

    searchComments() {
      this.currentPage = 1
      this.fetchComments()
    },

    resetFilters() {
      this.dateRange = null
      this.startTime = '0'
      this.endTime = Math.floor(Date.now() / 1000).toString()
      this.filterType = ''
      this.searchKeyword = ''
      this.currentPage = 1
      this.fetchComments()
    },

    handleSelectComment() {
      this.selectedComments = this.comments.filter((c) => c.selected)
    },

    handleBatchAction(command) {
      if (!this.hasSelectedComments) {
        this.$message.warning('请先选择要操作的评论')
        return
      }

      if (command === 'delete') {
        const count = this.selectedComments.length
        this.deleteTarget = { type: 'batch', data: this.selectedComments }
        this.deleteMessage = `确定要删除选中的 ${count} 条评论吗？`
        this.deleteDialogVisible = true
      } else if (command === 'export') {
        this.exportComments()
      }
    },

    exportComments() {
      // 实现评论导出功能
      this.$message.info('导出功能开发中...')
    },

    handleCurrentChange(page) {
      this.currentPage = page
      this.fetchComments()
    },

    handleSizeChange(size) {
      this.pageSize = size
      this.currentPage = 1
      this.fetchComments()
    },

    formatTimestamp(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp * 1000)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    getDefaultAvatar() {
      return 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.app-container {
  padding: 6px;
  background: transparent;
}

// 页面头部
.page-header {
  margin-bottom: 8px;
}

.header-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 8px 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-icon {
      font-size: 20px;
      color: #409eff;
      margin-right: 12px;
    }

    .header-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #374151;
      flex: 1;
    }

    .comment-stats {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      background: rgba(16, 185, 129, 0.1);
      color: #059669;

      i {
        font-size: 0.8rem;
      }
    }
  }
}

// 搜索区域
.search-section {
  margin-bottom: 8px;
}

.search-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .search-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .search-icon {
      font-size: 1.1rem;
      color: #409eff;
      margin-right: 6px;
    }

    .search-title {
      font-size: 1rem;
      font-weight: 600;
      color: #374151;
      flex: 1;
    }

    .reset-btn {
      color: #6b7280;
      padding: 4px 8px;

      &:hover {
        color: #409eff;
      }
    }
  }

  .search-content {
    .search-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 12px;
      margin-bottom: 8px;

      .search-item {
        label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: #374151;
          font-size: 0.8rem;
        }

        .date-picker,
        .status-select,
        .search-input {
          width: 100%;

          :deep(.el-input__inner) {
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;

            &:focus {
              border-color: #409eff;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
            }
          }
        }
      }
    }

    .search-actions {
      display: flex;
      gap: 12px;

      .search-btn {
        background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        transition: all 0.3s ease;        &:hover {
          box-shadow: 0 3px 12px rgba(64, 158, 255, 0.4);
        }
      }
    }
  }
}

// 评论区域
.comments-section {
  margin-bottom: 8px;
}

.comments-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

    .card-title {
      display: flex;
      align-items: center;
      font-size: 1rem;
      font-weight: 600;
      color: #374151;

      i {
        margin-right: 6px;
        color: #409eff;
      }
    }

    .batch-btn {
      color: #6b7280;

      &:hover {
        color: #409eff;
      }
    }
  }

  .comments-content {
    min-height: 400px;

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80px 20px;
      color: #9ca3af;

      i {
        font-size: 64px;
        margin-bottom: 6px;
      }

      p {
        margin: 0 0 20px 0;
        font-size: 1rem;
      }
    }
  }
}

.comment-list {
  .comment-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.02);
    }

    &:last-child {
      border-bottom: none;
    }

    .comment-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 10px 12px;

      .comment-user {
        display: flex;
        align-items: center;
        flex: 1;

        .comment-checkbox {
          margin-right: 12px;
        }

        .user-avatar {
          width: 44px;
          height: 44px;
          border-radius: 50%;
          overflow: hidden;
          margin-right: 12px;
          border: 2px solid #f1f5f9;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .user-info {
          .user-name {
            font-weight: 600;
            color: #374151;
            margin-bottom: 4px;
          }

          .comment-meta {
            display: flex;
            gap: 8px;
            font-size: 12px;
            color: #9ca3af;

            span {
              display: flex;
              align-items: center;
              gap: 4px;

              i {
                font-size: 12px;
              }
            }
          }
        }
      }

      .comment-actions {
        display: flex;
        gap: 8px;

        .reply-btn {
          color: #409eff;

          &:hover {
            background: rgba(64, 158, 255, 0.1);
          }
        }

        .delete-btn {
          color: #ef4444;

          &:hover {
            background: rgba(239, 68, 68, 0.1);
          }
        }
      }
    }

    .comment-content {
      padding: 0 24px 16px 92px;

      p {
        margin: 0;
        color: #374151;
        line-height: 1.6;
        word-wrap: break-word;
      }
    }

    .replies-section {
      background: #f8fafc;
      margin: 0 24px 16px 92px;
      border-radius: 8px;
      border: 1px solid #e2e8f0;

      .replies-header {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #e2e8f0;
        font-size: 0.8rem;
        font-weight: 500;
        color: #64748b;

        i {
          margin-right: 6px;
          color: #409eff;
        }
      }

      .replies-empty {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px;
        color: #9ca3af;
        font-size: 0.8rem;

        i {
          margin-right: 6px;
          font-size: 1.1rem;
        }
      }

      .replies-list {
        .reply-item {
          display: flex;
          padding: 12px 16px;
          border-bottom: 1px solid #e2e8f0;

          &:last-child {
            border-bottom: none;
          }

          .reply-avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 10px;
            flex-shrink: 0;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .reply-content {
            flex: 1;
            min-width: 0;

            .reply-header {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 4px;

              .reply-name {
                font-weight: 500;
                color: #374151;
                font-size: 13px;
              }

              .reply-time {
                color: #9ca3af;
                font-size: 11px;
              }
            }

            .reply-text {
              margin: 0;
              color: #64748b;
              font-size: 13px;
              line-height: 1.5;
              word-wrap: break-word;
            }
          }

          .reply-delete-btn {
            color: #ef4444;
            padding: 4px;
            margin-left: 8px;
            align-self: flex-start;

            &:hover {
              background: rgba(239, 68, 68, 0.1);
            }
          }
        }
      }
    }

    .loading-replies {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px;
      color: #9ca3af;
      font-size: 0.8rem;

      i {
        margin-right: 6px;
        animation: spin 1s linear infinite;
      }
    }
  }
}

// 分页样式
.pagination-section {
  display: flex;
  justify-content: right;
  margin-top: 8px;
}

.modern-pagination {
  :deep(.el-pager li) {
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.3s ease;

    &.active {
      background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
      border-color: transparent;
    }

    &:hover {
      background: rgba(64, 158, 255, 0.1);
    }
  }

  :deep(.btn-prev),
  :deep(.btn-next) {
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(64, 158, 255, 0.1);
    }
  }
}

// 删除对话框
:deep(.delete-dialog) {
  .el-dialog {
    border-radius: 8px;
  }

  .el-dialog__header {
    padding: 8px 10px 0;
  }

  .el-dialog__body {
    padding: 8px 10px;
  }

  .el-dialog__footer {
    padding: 12px 24px 20px;
  }
}

.delete-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;

  .delete-icon {
    font-size: 24px;
    color: #f59e0b;
    margin-top: 2px;
  }

  .delete-message {
    flex: 1;

    p {
      margin: 0 0 8px 0;
      color: #374151;
    }

    .delete-warning {
      color: #ef4444;
      font-size: 13px;
    }
  }
}

// 动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .search-row {
    grid-template-columns: 1fr !important;
  }

  .comment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .comment-actions {
      align-self: flex-end;
    }
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 4px;
  }

  .comment-content {
    padding-left: 24px !important;
  }

  .replies-section {
    margin-left: 24px !important;
  }

  .search-actions {
    flex-direction: column;

    .search-btn {
      width: 100%;
    }
  }
}
</style>

}

